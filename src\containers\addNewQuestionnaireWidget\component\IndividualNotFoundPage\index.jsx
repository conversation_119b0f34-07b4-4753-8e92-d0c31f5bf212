import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { HeadingAndDescription } from '@/components/HeadingAndDescription';
import strings from '@/utils/localization';

export const IndividualNotFoundPage = (props) => {
  const { validationData } = props;
  const { errors, control } = validationData;

  return (
    <Box>
      <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 3, mb: 2 }}>
        <CambianTooltip
          placement="right"
          title={<Typography variant="caption">{strings.individualNotFoundPageTooltip}</Typography>}
        >
          <span>{strings.individualNotFoundPage}</span>
        </CambianTooltip>
      </Typography>
      <Paper
        sx={{
          my: 1,
          border: '1px solid #F0F0F0',
          borderRadius: '5px 5px 0 0',
          px: { xs: 2, sm: 3 },
          py: 2,
        }}
      >
        <HeadingAndDescription
          control={control}
          errors={errors}
          headingName="individualNotFoundPage.heading"
          descriptionName="individualNotFoundPage.description"
        />
      </Paper>
    </Box>
  );
};
