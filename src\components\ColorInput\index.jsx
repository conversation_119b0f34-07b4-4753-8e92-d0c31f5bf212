import React from 'react';
import { Box, Typography, Stack } from '@mui/material';
import "./style.css";

export default function ColorInput({ title, onChange, input, id, name }) {
  return (
    <Stack direction="column" spacing={1} alignItems="center">
      <Box>
        <input 
        type="color" 
        id={id} 
        name={name} 
				className="color-input"
        value={input}
        onChange={onChange} 
        aria-label="backgroundColor" 
        aria-describedby="hash" />
      </Box>
      <Typography className="colorHeadings mt-2">{title}</Typography>
    </Stack>
  );
}
