import React from 'react';
import { Button, Dialog, DialogTitle, DialogContent, DialogActions, Typography, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import strings from '@/utils/localization';

function ConfirmationModal({
  modalOpen,
  handleClose,
  heading,
  modalDescription,
  handleConfirm,
  closeButtonText,
  confirmButtonText,
  handleCloseIconClick,
}) {
  return (
    <>
      <Dialog
        sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435, borderRadius: 0 } }}
        maxWidth="xs"
        open={modalOpen}
      >
        <DialogTitle
          sx={{
            fontSize: '1.5rem',
            mb: 1,
            pb: 0,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {heading}
          {handleCloseIconClick && (
            <IconButton
              aria-label="close"
              onClick={handleCloseIconClick}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          )}
        </DialogTitle>
        <DialogContent sx={{ pt: 1, pb: 2 }}>
          <Typography>{modalDescription}</Typography>
        </DialogContent>
        <DialogActions sx={{ m: 1 }}>
          <Button
            autoFocus
            variant="outlined"
            onClick={handleClose}
            color={closeButtonText === strings.discard ? 'error' : 'primary'}
          >
            {closeButtonText}
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirm}
            color={confirmButtonText === strings.delete ? 'error' : 'primary'}
          >
            {confirmButtonText}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default ConfirmationModal;
