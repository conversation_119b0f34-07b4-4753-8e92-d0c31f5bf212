import {
  ORGANI<PERSON><PERSON><PERSON>_ID,
  BOOKING_WIDGET_ID,
  QUESTIONNAIRE_WIDGET_ID,
  WIDGET_TYPE,
  REGISTRATION_WIDGET_ID,
  QUESTIONNAIRE_ID,
  WIDGET_ID,
} from '@/utils/constants';

export const GET_ORGANIZATION_DETAILS = `/organization/${ORGANIZATION_ID}`;
export const CREATE_BOOKING_WIDGET = `/${ORGANIZATION_ID}/bookingWidget`;
export const IMPORT_BOOKING_WIDGET = `/${ORGANIZATION_ID}/importBookingWidget`;
export const GET_ALL_BOOKING_WIDGET = `/${ORGANIZATION_ID}/bookingWidgets`;
export const BOOKING_WIDGET_ENDPOINT = `/${ORGANIZATION_ID}/bookingWidget/${BOOKING_WIDGET_ID}`;
export const CREATE_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/questionnaireWidget`;
export const IMPORT_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/importQuestionnaireWidget`;
export const GET_ALL_QUESTIONNAIRE_WIDGET = `/${ORGANIZATION_ID}/questionnaireWidgets`;
export const QUESTIONNAIRE_WIDGET_ENDPOINT = `/${ORGANIZATION_ID}/questionnaireWidget/${QUESTIONNAIRE_WIDGET_ID}`;
export const CREATE_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidget`;
export const GET_ALL_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidgets`;
export const GET_REGISTRATION_WIDGET = `/${ORGANIZATION_ID}/registrationWidget/${REGISTRATION_WIDGET_ID}`;

export const DELETE_WIDGET = `/${ORGANIZATION_ID}/${WIDGET_TYPE}/${WIDGET_ID}`;

// Domain-based URLs
export const NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL = 'https://dev.cambianservices.ca/org-data';
export const NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL = 'https://dev.cambianservices.ca/scheduler-booking';
export const NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL = 'https://dev.cambianservices.ca/org-artifact-repo';
export const NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL = 'https://dev.cambianservices.ca/net-artifact-repo';
export const NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL = 'https://dev.cambianservices.ca/ind-cdr';
export const NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL = 'https://dev.cambianservices.ca/org-pre-cdr';
export const NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL = 'https://dev.cambianservices.ca/connection-index';
export const NEXT_PUBLIC_CLIENT_INDEX_BASE_URL = 'https://dev.cambianservices.ca/client-index';
export const NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL = 'https://dev.cambianservices.ca/org-doc-gen';
export const NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL = 'https://dev.cambianservices.ca/org-messaging';
export const NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL = 'https://dev.cambianservices.ca/ind-data';

// Endpoint paths to be used with the domain-based URLs
export const GET_LOCATIONS_LIST = `/organizations/${ORGANIZATION_ID}/locations`;
export const GET_SERVICES_LIST = `/organizations/${ORGANIZATION_ID}/services`;
export const GET_ORGANIZATION = `/organization/registry/978456c9-7ab5-4a64-80cc-aa11f8a86cbc`;
