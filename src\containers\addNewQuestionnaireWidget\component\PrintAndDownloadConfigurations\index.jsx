import React from 'react';
import { Checkbox, FormControlLabel, Grid, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const PrintAndDownloadConfigurations = (props) => {
  const { printIconState, downloadIconState, validationData, saveIconState, enableLogin } = props;
  const { control } = validationData;

  return (
    <Grid container alignItems="center">
      <Grid item xs={4} lg={3}>
        <Typography>
          <CambianTooltip placement="right" title={<Typography variant="caption">{strings.iconsTooltips}</Typography>}>
            <span>{strings.icons}</span>
          </CambianTooltip>
        </Typography>
      </Grid>
      <Grid item xs={8} lg={9}>
        <Controller
          control={control}
          name={printIconState}
          render={({ field: { ref, ...field } }) => {
            return (
              <FormControlLabel
                label={strings.print || 'Print'}
                control={
                  <Checkbox
                    checked={field.value || false}
                    name={printIconState}
                    onChange={(event, val) => field.onChange(val)}
                  />
                }
              />
            );
          }}
        />
        <Controller
          control={control}
          name={downloadIconState}
          render={({ field: { ref, ...field } }) => {
            return (
              <FormControlLabel
                label={strings.download || 'Download'}
                control={
                  <Checkbox
                    checked={field.value || false}
                    name={downloadIconState}
                    onChange={(event, val) => field.onChange(val)}
                  />
                }
              />
            );
          }}
        />
        {enableLogin && (
          <Controller
            control={control}
            name={saveIconState} //to be implmented
            render={({ field: { ref, ...field } }) => {
              return (
                <FormControlLabel
                  label={strings.save || 'Save'}
                  control={
                    <Checkbox
                      checked={!enableLogin ? false : field.value || false}
                      name={saveIconState}
                      onChange={(event, val) => field.onChange(val)}
                    />
                  }
                />
              );
            }}
          />
        )}
      </Grid>
    </Grid>
  );
};
