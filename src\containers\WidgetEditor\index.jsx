import React, { useMemo, useState } from 'react';
import { Widgets } from '../Widget';
import { pages } from '@/utils/constants/common';
import { AddNewQuestionnaireWidget } from '../addNewQuestionnaireWidget';
import { extractGUID } from '@/utils/commonUtility';
import { AddNewBookingWidget } from '../addNewBookingWidget';
import { AddNewRegistrationWidget } from '../addNewRegistrationWidget';
import { BOOKING_CAPS, QUESTIONNAIRE_CAPS, REGISTRATION_CAPS } from '@/utils/constants';

export const WidgetEditor = (props) => {
  const {
    widgetsList,
    organizationId,
    widgetBaseUrl,
    handleImportWidgetCallback,
    handleDeleteWidgetCallback,
    handleDuplicateWidgetCallback,
    handlePreviewWidgetCallback,
    handleSaveOrUpdateWidgetCallback,
    allIdTypes,
    orgRequiredIdTypes,
    fetchBookingWidgetById,
    fetchQuestionnaireWidgetById,
    fetchRegistrationWidgetById,
    handleExportWidgetCallback,
    // booking widget props
    bookingWidgetFields,
    servicesList,
    locationsList,

    // questionnaire widget props
    questionnaireWidgetFields = [],
    publicQuestionnaireList = [],
    privateQuestionnaireList = [],
    handleQuestionnaireDropdownCallback,
    questionnaireDefinition,
    setQuestionnaireDefinition,

    //registration widget props
    registrationWidgetFields = [],
  } = props;

  const [currentPage, setCurrentPage] = React.useState(pages.widgetList);
  const [currentEditingWidgetDetails, setCurrentEditingWidgetDetails] = useState();

  const handleNavigation = (page) => {
    if (page === pages.widgetList) {
      setCurrentEditingWidgetDetails();
    }

    setCurrentPage(page);
  };

  const fetchWidgetWithLanguage = async (params, callback) => {
    const { organizationId, widgetId, widgetType, language } = params;
    console.log('fetchWidgetWithLanguage called with params:', params);

    try {
      let widgetData;
      if (widgetType === 'bookingWidget') {
        widgetData = await fetchBookingWidgetById(widgetId, organizationId, language);
      } else if (widgetType === 'questionnaireWidget') {
        widgetData = await fetchQuestionnaireWidgetById(widgetId, organizationId, language);
      } else if (widgetType === 'registrationWidget') {
        widgetData = await fetchRegistrationWidgetById(widgetId, organizationId, language);
      }

      if (callback) {
        callback(widgetData); // May be null if 404 occurred
      }
      if (widgetData) {
        setCurrentEditingWidgetDetails(widgetData);
      }
    } catch (error) {
      console.error(`Error fetching ${widgetType} for language ${language}:`, error);
      if (callback) {
        console.log('Calling callback with null due to error');
        callback(null);
      }
    }
  };

  const handleEditWidget = async (page, widgetData) => {
    let orgId = extractGUID(widgetData.PK);
    let widgetId = extractGUID(widgetData.SK);
    let widgetAllData;
    if (widgetData.widgetType === BOOKING_CAPS) {
      widgetAllData = await fetchBookingWidgetById(widgetId, orgId);
    } else if (widgetData.widgetType === QUESTIONNAIRE_CAPS) {
      widgetAllData = await fetchQuestionnaireWidgetById(widgetId, orgId);
    } else if (widgetData.widgetType === REGISTRATION_CAPS) {
      widgetAllData = await fetchRegistrationWidgetById(widgetId, orgId);
    }
    setCurrentEditingWidgetDetails(widgetAllData);
    setCurrentPage(page);

    if (widgetAllData?.widgetType === QUESTIONNAIRE_CAPS) {
      try {
        const response = await handleQuestionnaireDropdownCallback(
          widgetAllData?.repository,
          widgetAllData?.questionnaire?.artifactId,
        );
        setQuestionnaireDefinition(response?.questionnaire);
      } catch (e) {
        throw new Error('Error occured while fetching questionnaire definition', e);
      }
    }
  };

  const handleSaveOrUpdateWithDetailsUpdate = async (widgetData, widgetType) => {
    const response = await handleSaveOrUpdateWidgetCallback(widgetData, widgetType);

    if (response.success && response.widgetDetails) {
      setCurrentEditingWidgetDetails(response.widgetDetails);
    }

    return response;
  };

  const GetPage = () => {
    if (currentPage === pages.widgetList) {
      return (
        <>
          <Widgets
            widgetsList={widgetsList}
            widgetBaseUrl={widgetBaseUrl}
            handleNavigationCallback={handleNavigation}
            handleEditWidgetCallback={handleEditWidget}
            handleImportWidgetCallback={handleImportWidgetCallback}
            handlePreviewWidgetCallback={handlePreviewWidgetCallback}
            handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
            handleDeleteWidgetCallback={handleDeleteWidgetCallback}
            handleExportWidgetCallback={handleExportWidgetCallback}
            organizationId={organizationId}
          />
        </>
      );
    } else if (currentPage === pages.addNewQuestionnaireWidget) {
      return (
        <>
          <AddNewQuestionnaireWidget
            handleNavigationCallback={handleNavigation}
            existingWidgetData={currentEditingWidgetDetails}
            handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWithDetailsUpdate}
            widgetsList={widgetsList}
            questionnaireWidgetFields={questionnaireWidgetFields}
            publicQuestionnaireList={publicQuestionnaireList}
            privateQuestionnaireList={privateQuestionnaireList}
            handleQuestionnaireDropdownCallback={handleQuestionnaireDropdownCallback}
            questionnaireDefinition={questionnaireDefinition}
            setQuestionnaireDefinition={setQuestionnaireDefinition}
            allIdTypes={allIdTypes}
            orgRequiredIdTypes={orgRequiredIdTypes}
            fetchWidgetWithLanguage={fetchWidgetWithLanguage}
          />
        </>
      );
    } else if (currentPage === pages.addNewBookingWidget) {
      return (
        <>
          <AddNewBookingWidget
            widgetsList={widgetsList}
            existingWidgetData={currentEditingWidgetDetails}
            bookingWidgetFields={bookingWidgetFields}
            servicesList={servicesList}
            locationsList={locationsList}
            handleNavigationCallback={handleNavigation}
            handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWithDetailsUpdate}
            allIdTypes={allIdTypes}
            orgRequiredIdTypes={orgRequiredIdTypes}
            fetchWidgetWithLanguage={fetchWidgetWithLanguage}
          />
        </>
      );
    } else if (currentPage === pages.addNewRegistrationWidget) {
      return (
        <>
          <AddNewRegistrationWidget
            widgetsList={widgetsList}
            handleNavigationCallback={handleNavigation}
            existingWidgetData={currentEditingWidgetDetails}
            handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWithDetailsUpdate}
            registrationWidgetFields={registrationWidgetFields}
            allIdTypes={allIdTypes}
            orgRequiredIdTypes={orgRequiredIdTypes}
            fetchWidgetWithLanguage={fetchWidgetWithLanguage}
          />
        </>
      );
    }
  };

  return <>{GetPage()}</>;
};
