import React from 'react';
import { HeadingAndDescription } from '@/components/HeadingAndDescription';

export const HeadingAndDescriptionAction = (props) => {
  const {
    data: { action, index },
    validationData,
    enableButton,
  } = props;

  const { register, errors, control } = validationData;

  return (
    <div style={{ marginTop: '1rem' }}>
      <HeadingAndDescription
        headingName={`actionData[${index}].actionHeading`}
        descriptionName={`actionData[${index}].actionDescription`}
        register={register}
        control={control}
        errors={errors}
        headingError={errors && errors.actionData?.length && errors.actionData[index]?.actionHeading}
        descriptionError={errors && errors.actionData?.length && errors.actionData[index]?.actionDescription}
        enableButton={enableButton}
        required={true}
      />
    </div>
  );
};
