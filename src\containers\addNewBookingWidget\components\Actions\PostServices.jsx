import React from 'react';
import { Grid, FormControl, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { MultiSelect } from '../Services/MultiSelect';
import strings from '@/utils/localization';

export const PostServices = (props) => {
  const { servicesList, validationData, fieldName, validationError, selectedPostServices, handleSelectPostServices } =
    props;

  const getOptionLabel = (option) => `${option.name}`;

  const handleToggleOption = (selectedOptions) => {
    handleSelectPostServices(selectedOptions);
  };

  const handleClearOptions = () => {
    handleSelectPostServices([]);
  };

  const handleSelectAll = (isSelected, field) => {
    if (isSelected) {
      field.onChange(servicesList);
      handleSelectPostServices(servicesList);
    } else {
      field.onChange([]);
      handleClearOptions();
    }
  };

  return (
    <Grid container alignItems="center">
      <Grid item xs={4} lg={3}>
        <Typography>
          <span>
            {strings.services}
            {' *'}
          </span>
        </Typography>
      </Grid>
      <Grid item xs={7} lg={9}>
        <FormControl fullWidth>
          <MultiSelect
            fieldName={fieldName}
            items={servicesList}
            getOptionLabel={getOptionLabel}
            selectedValues={selectedPostServices}
            placeholder={strings.servicesPlaceholder}
            selectAllLabel="Select all"
            onToggleOption={handleToggleOption}
            onClearOptions={handleClearOptions}
            onSelectAll={handleSelectAll}
            validationData={validationData}
            validationError={validationError}
          />
        </FormControl>
      </Grid>
    </Grid>
  );
};
