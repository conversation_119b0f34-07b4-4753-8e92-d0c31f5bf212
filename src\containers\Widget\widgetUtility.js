export const mergeAndSortWidgets = (questionnaireWidgets, bookingWidgets, registrationWidgets, key) => {
  let mergedWidgets = [];

  if (!questionnaireWidgets && !bookingWidgets && !registrationWidgets) return mergedWidgets;

  mergedWidgets = mergedWidgets.concat(bookingWidgets);
  mergedWidgets = mergedWidgets.concat(questionnaireWidgets);
  mergedWidgets = mergedWidgets.concat(registrationWidgets);

  mergedWidgets.sort(function (a, b) {
    if (b[key] < a[key]) {
      return -1;
    } else if (a[key] > b[key]) {
      return 1;
    } else {
      return 0;
    }
  });

  return mergedWidgets;
};
