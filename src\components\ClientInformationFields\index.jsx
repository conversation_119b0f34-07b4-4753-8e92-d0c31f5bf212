import React, { useState, useEffect } from 'react';
import { Grid, Typography, FormControlLabel, Checkbox, Box, TextField } from '@mui/material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import DehazeIcon from '@mui/icons-material/Dehaze';
import strings from '@/utils/localization';
import { HealthCareIdTypes } from './HealthCareIdTypes';

export const ClientInformationFields = (props) => {
  const {
    handleSelectFields,
    fieldsList,
    setFieldsList,
    validationData,
    selectedIdTypes,
    setSelectedIdTypes,
    allIdTypes,
    orgRequiredIdTypes,
  } = props;

  const [editingIndex, setEditingIndex] = useState(-1);

  const currentLanguage = validationData?.watch('currentLanguage');

  useEffect(() => {
    if (!fieldsList.length || !currentLanguage) return;
    const updatedFields = fieldsList.map((field) => {
      if (isDefaultLabel(field.display, field.code)) {
        return { ...field, display: getFieldLabel(field.code) };
      }
      return field;
    });
    const hasChanges = updatedFields.some((field, i) => field.display !== fieldsList[i].display);
    if (hasChanges) {
      setFieldsList(updatedFields);
      handleSelectFields(updatedFields.filter((field) => field.checked));
    }
  }, [currentLanguage, fieldsList, setFieldsList, handleSelectFields]);

  const fieldCodeToStringKey = {
    FIRST_NAME: 'firstName',
    MIDDLE_NAME: 'middleName',
    LAST_NAME: 'lastName',
    DATE_OF_BIRTH: 'dateOfBirth',
    GENDER: 'gender',
    EMAIL: 'email',
    PHONE: 'phone',
    PREFERRED_CONTACT_METHOD: 'preferredContactMethod',
    NOTIFICATIONS: 'notifications',
    ADDRESS: 'address',
    IDENTIFICATION: 'identification',
  };

  const getFieldLabel = (fieldCode) => {
    const stringKey = fieldCodeToStringKey[fieldCode];
    return stringKey ? strings[stringKey] : fieldCode;
  };

  const isDefaultLabel = (display, code) => {
    const stringKey = fieldCodeToStringKey[code];
    if (!stringKey) return false;

    return Object.values(strings._props).some((lang) => lang[stringKey] === display);
  };

  const handleChange = (e, field) => {
    let updatedFieldsList = [...fieldsList];
    updatedFieldsList = updatedFieldsList.map((updatedField) => {
      if (updatedField.SK === field.SK) {
        updatedField.checked = e.target.checked;
        if (e.target.checked === false) {
          updatedField.isMandatory = false;
          updatedField.allowMultiple = false;
        }
      }
      return updatedField;
    });
    validationData.setValue(
      'fields',
      updatedFieldsList.filter((field) => field.checked),
    );
    setFieldsList(updatedFieldsList);
    handleSelectFields(updatedFieldsList.filter((field) => field.checked));
  };

  const handleLabelChange = (event, field) => {
    let updatedFieldsList = [...fieldsList];
    updatedFieldsList = updatedFieldsList.map((updatedField) => {
      if (updatedField.SK === field.SK) {
        updatedField.display = event.target.value;
      }
      return updatedField;
    });
    setFieldsList(updatedFieldsList);
    handleSelectFields(
      updatedFieldsList.filter((field) => field.checked).map((field, index) => ({ ...field, position: index + 1 })),
    );
  };

  const handleMandatory = (e, field) => {
    let updatedFieldsList = [...fieldsList];
    updatedFieldsList = updatedFieldsList.map((updatedField) => {
      if (updatedField.SK === field.SK && updatedField.checked) {
        updatedField.isMandatory = e.target.checked;
      }
      return updatedField;
    });

    setFieldsList(updatedFieldsList);
    handleSelectFields(updatedFieldsList.filter((field) => field.checked));
  };

  const handleMultiple = (e, field) => {
    let updatedFieldsList = [...fieldsList];
    updatedFieldsList = updatedFieldsList.map((updatedField) => {
      if (updatedField.SK === field.SK && updatedField.checked) {
        updatedField.allowMultiple = e.target.checked;
      }
      return updatedField;
    });
    setFieldsList(updatedFieldsList);
    handleSelectFields(updatedFieldsList.filter((field) => field.checked));
  };

  const handleIdTypeChange = (newSelectedIdTypesArray) => {
    const formattedSelectedIdTypes = newSelectedIdTypesArray.map((selectedIdType) => {
      const idTypeData = allIdTypes.find((type) => type.idType === selectedIdType.idType);

      return {
        idType: selectedIdType.idType,
        issuers: selectedIdType.issuers,
        required: selectedIdType.required,
      };
    });

    setSelectedIdTypes(formattedSelectedIdTypes);
  };

  const renderFields = (filteredFieldsList) => {
    const allowedFields = fieldsList
      .filter((field) => field.systemRequired)
      .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

    const onDragEnd = (result) => {
      if (!result.destination) return;

      const newFieldsList = [...fieldsList];
      const sourceIndex = result.source.index;
      const destinationIndex = result.destination.index;

      const allowedFields = newFieldsList
        .filter((field) => field.systemRequired)
        .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

      const [draggedField] = allowedFields.splice(sourceIndex, 1);
      allowedFields.splice(destinationIndex, 0, draggedField);
      allowedFields.forEach((field, index) => {
        const mainField = newFieldsList.find((f) => f.SK === field.SK);
        if (mainField) {
          mainField.position = index + 1;
        }
      });

      setFieldsList(newFieldsList);
      handleSelectFields(
        newFieldsList.filter((field) => field.checked).sort((a, b) => (a.position ?? 0) - (b.position ?? 0)),
      );
      validationData.setValue(
        'fields',
        newFieldsList.filter((field) => field.checked).sort((a, b) => (a.position ?? 0) - (b.position ?? 0)),
      );
    };

    if (allowedFields.length > 0) {
      return (
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="fields">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {allowedFields.map((field, index) => (
                  <Draggable
                    key={field.code}
                    draggableId={field.code}
                    index={index}
                    isDragDisabled={editingIndex !== -1}
                  >
                    {(provided) => (
                      <Grid
                        id={`${field.code}-draggable`}
                        container
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                        alignItems="center"
                      >
                        <Grid item xs={3} md={3}>
                          <Typography variant="body2">{field.code}</Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <TextField
                            fullWidth
                            size="small"
                            value={field.display}
                            onChange={(e) => handleLabelChange(e, field)}
                          />
                        </Grid>
                        <Grid item xs={1} md={2} pl={8.5}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                id={`${field.code}-display`}
                                checked={(field.systemMandatory && field.systemRequired) || field.checked}
                                onChange={(e) => handleChange(e, field)}
                                disabled={field.systemMandatory}
                              />
                            }
                            label=""
                          />
                        </Grid>
                        <Grid item xs={1} md={1.5}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                id={`${field.code}-mandatory`}
                                checked={field.systemMandatory || field.isMandatory}
                                onChange={(e) => handleMandatory(e, field)}
                                disabled={!field.checked || field.systemMandatory}
                              />
                            }
                            label=""
                          />
                        </Grid>
                        <Grid item xs={1} md={1.5}>
                          {(field.code === 'IDENTIFICATION' ||
                            field.code === 'EMAIL' ||
                            field.code === 'PHONE' ||
                            field.code === 'ADDRESS') && (
                            <FormControlLabel
                              control={
                                <Checkbox
                                  id={`${field.code}-multiple`}
                                  checked={field.allowMultiple || (field.systemAllowMultiple && field.systemMandatory)}
                                  onChange={(e) => handleMultiple(e, field)}
                                  disabled={!field.checked}
                                />
                              }
                              label=""
                            />
                          )}
                        </Grid>
                        <Grid item xs={1} md={1}>
                          <DehazeIcon id={`${field.code}-drag-handle`} />
                        </Grid>
                        {field.code === 'IDENTIFICATION' && field.checked && (
                          <Grid item xs={12}>
                            <HealthCareIdTypes
                              validationData={validationData}
                              onIdTypeChange={handleIdTypeChange}
                              selectedIdTypes={selectedIdTypes}
                              setSelectedIdTypes={setSelectedIdTypes}
                              allIdTypes={allIdTypes}
                              orgRequiredIdTypes={orgRequiredIdTypes}
                            />
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      );
    } else {
      return (
        <div className="row">
          <p className="emptyMessage">{strings.emptyFieldsText}</p>
        </div>
      );
    }
  };

  return (
    <Box id="fieldsList">
      <Grid container sx={{ color: 'text.subHeading', mt: 2 }}>
        <Grid item xs={3} md={3}>
          <Typography variant="body2">{strings.identifier}</Typography>
        </Grid>
        <Grid item xs={6} md={3.7}>
          <Typography variant="body2">{strings.field}</Typography>
        </Grid>
        <Grid item xs={1} md={1.3}>
          <Typography variant="body2">{strings.display}</Typography>
        </Grid>
        <Grid item xs={1} md={1.5}>
          <Typography variant="body2">{strings.mandatory}</Typography>
        </Grid>
        <Grid item xs={1} md={1.5}>
          <Typography variant="body2">{strings.multiple}</Typography>
        </Grid>
        <Grid item xs={1} md={1}>
          <Typography variant="body2">{strings.reorder}</Typography>
        </Grid>
      </Grid>
      {renderFields(fieldsList)}
    </Box>
  );
};
