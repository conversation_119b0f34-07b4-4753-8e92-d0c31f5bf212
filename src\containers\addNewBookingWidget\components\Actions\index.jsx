import React, { useMemo, useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  Switch,
  Button,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import CloseIcon from '@mui/icons-material/Close';
import { Controller } from 'react-hook-form';
import { PostServices } from './PostServices';
import { RedirectToUrl } from './RedirectToUrl';
import { Add } from '@mui/icons-material';
import { RedirectToWidget } from './RedirectToWidget';
import { CallService } from './CallService';
import { HeadingAndDescriptionAction } from './HeadingAndDescriptionAction';
import { MAX_CONDITION_LIMIT } from '@/utils/constants';
import strings from '@/utils/localization';
import { actionFieldsData } from '../../data/actionFieldsData';

export const Actions = (props) => {
  const { allAvailableWidgetsList, validationData, selectedServicesList, existingWidgetData } = props;
  const { control, errors, actionData, addField, removeField, setValue, getValues, trigger } = validationData;
  const { action } = existingWidgetData || {};

  const formActionEnabled = getValues('isActionEnabled');
  const [actionEnabled, setActionEnabled] = useState(formActionEnabled ?? action?.enabled ?? false);
  const [widgetsList, setWidgetsList] = useState([]);
  const [actionFields, setActionFields] = useState([...actionData]);

  useEffect(() => {
    const formValue = getValues('isActionEnabled');
    setActionEnabled(formValue !== undefined ? formValue : action?.enabled ?? false);
    const savedActionData = getValues('actionData');
    if (savedActionData?.length) {
      setActionFields(savedActionData);
    }
  }, []);

  const handleRemoveField = (index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData.splice(index, 1);
      return updatedData;
    });

    const currentValues = getValues('actionData');
    const updatedValues = [...currentValues];
    updatedValues.splice(index, 1);

    removeField(index);
    setValue('actionData', updatedValues, { shouldDirty: true });
  };

  useMemo(() => {
    const savedActionData = getValues('actionData');
    if (existingWidgetData) {
      setActionEnabled(action?.enabled || false);
      setActionFields(action?.enabled ? action?.actionConditions : savedActionData || [...actionData]);
    } else if (savedActionData?.length) {
      setActionFields(savedActionData);
    }
  }, [existingWidgetData]);

  useMemo(() => {
    if (selectedServicesList?.length) {
      const formActionData = getValues('actionData');
      const updatedActionData = formActionData?.map((action, index) => {
        if (action.selectedServices?.length) {
          const selectedServicesCode = new Set(action.selectedServices.map((service) => service.id));
          const updatedServicesList = selectedServicesList.filter((service) => selectedServicesCode.has(service.id));
          return {
            ...action,
            selectedServices: updatedServicesList,
          };
        }
        return action;
      });

      setActionFields(updatedActionData);
      setValue('actionData', updatedActionData);
    }
  }, [selectedServicesList]);

  const handleSelectPostServices = (index, services) => {
    const formActionData = getValues('actionData');
    let updatedActionData = [...formActionData];
    updatedActionData[index] = {
      ...updatedActionData[index],
      selectedServices: services,
    };
    setValue('actionData', updatedActionData);
    trigger(`actionData[${index}].selectedServices`);

    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedServices: services,
      };
      return updatedData;
    });
  };

  const handleWidgetsList = (widgets) => {
    setWidgetsList(widgets);
  };

  const handleActionDropdown = (e, index) => {
    const selectedAction = e.target.value;
    const formActionData = getValues('actionData');
    let updatedActionData = [...formActionData];
    updatedActionData[index] = {
      ...updatedActionData[index],
      selectedAction: selectedAction,
    };
    if (selectedAction === 'Widget') {
      updatedActionData[index].selectedWidgetType = '';
      updatedActionData[index].selectedWidget = '';
    }
    setValue('actionData', updatedActionData);

    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedAction: selectedAction,
      };
      if (selectedAction === 'Widget') {
        updatedData[index].selectedWidgetType = '';
        updatedData[index].selectedWidget = '';
      }
      return updatedData;
    });
  };

  const handleTargetDropdown = (item, { target: { value } }, index) => {
    const selectedTarget = value.value;
    const formActionData = getValues('actionData');
    let updatedActionData = [...formActionData];
    updatedActionData[index] = {
      ...updatedActionData[index],
      selectedTarget: selectedTarget,
    };
    setValue('actionData', updatedActionData);

    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedTarget: selectedTarget,
      };
      return updatedData;
    });
  };

  const handleAddAction = () => {
    addField();
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData, { ...actionFieldsData, default: false }];
      return updatedData;
    });
  };

  return (
    <Box>
      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.actionsTooltip}</Typography>}
            >
              <span>
                {strings.actions}
                {actionEnabled && ' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isActionEnabled"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  name="isActionEnabled"
                  checked={actionEnabled}
                  onChange={(event, val) => {
                    setActionEnabled(val);
                    field.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>

      {actionEnabled && (
        <Grid container sx={{ mt: 2 }} alignItems="center">
          <Grid item xs={4} lg={3}>
            <Typography>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.actionButtonNameTooltip}</Typography>}
              >
                <span>{strings.button}</span>
              </CambianTooltip>
            </Typography>
          </Grid>

          <Grid item xs={7} lg={9}>
            <Controller
              name="actionButtonText"
              control={control}
              render={({ field: { ref, ...field } }) => (
                <TextField
                  {...field}
                  fullWidth
                  size="small"
                  name="actionButtonText"
                  type="text"
                  onChange={(event) => {
                    if (event.target.value.trim()) {
                      field.onChange(event.target.value);
                    } else {
                      field.onChange(event.target.value.trim());
                    }
                  }}
                  placeholder={strings.next}
                  error={!!errors?.buttonName}
                  helperText={errors?.buttonName?.message}
                />
              )}
            />
          </Grid>
        </Grid>
      )}

      {actionEnabled && actionData && actionData.length
        ? actionData.map((action, index) => (
            <Paper sx={{ pb: 2, my: 2 }} key={action.id}>
              <div>
                <Grid
                  container
                  alignItems="center"
                  justifyContent="space-between"
                  sx={{ bgcolor: 'background.secondary' }}
                >
                  <Grid item xs={11} sm={10} md={11} sx={{ pl: 1, py: 1 }}>
                    <Typography variant="body1">{action.default ? strings.default : `Condition ${index}`}</Typography>
                  </Grid>
                  <Grid item xs={1} sx={{ textAlign: 'right', pr: 1 }}>
                    {actionData?.length > 1 && !action.default && (
                      <CloseIcon
                        onClick={() => handleRemoveField(index)}
                        sx={{
                          cursor: 'pointer',
                          fontSize: '20px',
                        }}
                      />
                    )}
                  </Grid>
                </Grid>
              </div>

              <Grid container sx={{ p: 1 }}>
                <Grid item xs={12}>
                  {!action.default && (
                    <Box sx={{ mt: 2 }}>
                      <PostServices
                        validationData={validationData}
                        validationError={
                          errors?.actionData !== undefined ? errors?.actionData[index]?.selectedServices?.message : ''
                        }
                        servicesList={selectedServicesList}
                        selectedPostServices={actionFields[index]?.selectedServices}
                        handleSelectPostServices={(services) => handleSelectPostServices(index, services)}
                        fieldName={`actionData[${index}].selectedServices`}
                        index={index}
                      />
                    </Box>
                  )}
                  <Grid container alignItems="center" sx={{ mt: 2 }}>
                    <Grid item xs={4} lg={3}>
                      <Typography>
                        <CambianTooltip
                          placement="right"
                          title={
                            <Typography variant="caption">
                              {action.default ? strings.defaultActionTooltip : strings.selectedActionTooltip}
                            </Typography>
                          }
                        >
                          <span>
                            {strings.action}
                            {actionEnabled && ' *'}
                          </span>
                        </CambianTooltip>
                      </Typography>
                    </Grid>
                    <Grid item xs={7} lg={9}>
                      <FormControl fullWidth>
                        <Controller
                          name={`actionData[${index}].selectedAction`}
                          control={control}
                          mode="onBlur"
                          render={({ field: { ref, value, ...field } }) => (
                            <Select
                              sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                              name={`actionData[${index}].selectedAction`}
                              error={
                                errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedAction : false
                              }
                              displayEmpty
                              value={value}
                              onChange={(event, value) => {
                                handleActionDropdown(event, index);
                                field.onChange(value?.props?.value);
                              }}
                              renderValue={(selected) => {
                                if (typeof selected === 'undefined' || selected.length === 0) {
                                  return (
                                    <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>
                                      {strings.actionPlaceholder}
                                    </Typography>
                                  );
                                }
                                return selected;
                              }}
                              inputProps={{ 'aria-label': 'Without label' }}
                              size="small"
                            >
                              {action?.actionDropDown?.map((actionOption) => (
                                <MenuItem value={actionOption.value} key={actionOption.id}>
                                  {actionOption.label}
                                </MenuItem>
                              ))}
                            </Select>
                          )}
                        />
                        <FormHelperText error>
                          {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedAction?.message : ''}
                        </FormHelperText>
                      </FormControl>
                    </Grid>
                  </Grid>
                  {actionFields[index]?.selectedAction === 'Page' && (
                    <HeadingAndDescriptionAction
                      validationData={validationData}
                      data={{ action, index }}
                      actionFieldsData={actionFieldsData}
                      enableButton={false}
                    />
                  )}
                  {actionFields[index]?.selectedAction === 'URL' && (
                    <RedirectToUrl action={action} index={index} validationData={validationData} />
                  )}
                  {actionFields[index]?.selectedAction === 'Widget' && (
                    <RedirectToWidget
                      data={{ action, index }}
                      actionFields={actionFields}
                      setActionFields={setActionFields}
                      validationData={validationData}
                      widgetsList={widgetsList}
                      handleWidgetsList={handleWidgetsList}
                      allAvailableWidgetsList={allAvailableWidgetsList}
                    />
                  )}
                  {(actionFields[index]?.selectedAction === 'Service' ||
                    actionFields[index]?.selectedAction === 'Call a service in background') && (
                    <CallService
                      validationData={validationData}
                      data={{ action, index }}
                      actionFields={actionFields}
                      setActionFields={setActionFields}
                    />
                  )}
                  {(actionFields[index]?.selectedAction === 'URL' ||
                    actionFields[index]?.selectedAction === 'Widget') && (
                    <>
                      <Grid container alignItems="center" sx={{ mt: 2 }}>
                        <Grid item xs={4} lg={3}>
                          <Typography>
                            <span>
                              {strings.target} {actionEnabled && ' *'}
                            </span>
                          </Typography>
                        </Grid>
                        <Grid item xs={7} lg={9}>
                          <FormControl fullWidth>
                            <Controller
                              name={`actionData[${index}].selectedTarget`}
                              control={control}
                              mode="onBlur"
                              render={({ field: { ref, value, ...field } }) => (
                                <Select
                                  id="selected-target"
                                  sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                                  name={`actionData[${index}].selectedTarget`}
                                  size="small"
                                  displayEmpty
                                  onChange={(event, value) => {
                                    handleTargetDropdown(action, event, index);
                                    field.onChange(value?.props?.value?.label);
                                  }}
                                  value={value}
                                  renderValue={(selected) => {
                                    if (typeof selected === 'undefined' || selected.length === 0) {
                                      return (
                                        <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>
                                          {strings.targetPlaceholder}
                                        </Typography>
                                      );
                                    }
                                    return selected;
                                  }}
                                  inputProps={{ 'aria-label': 'Without label' }}
                                  error={
                                    errors?.actionData !== undefined
                                      ? !!errors?.actionData[index]?.selectedTarget?.message
                                      : false
                                  }
                                >
                                  {action?.targetDropDown?.map((target) => (
                                    <MenuItem value={target} key={target.id}>
                                      {target.label}
                                    </MenuItem>
                                  ))}
                                </Select>
                              )}
                            />
                            <FormHelperText error>
                              {errors?.actionData !== undefined
                                ? errors?.actionData[index]?.selectedTarget?.message
                                : ''}
                            </FormHelperText>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </>
                  )}
                </Grid>
              </Grid>
            </Paper>
          ))
        : ''}
      {actionEnabled && actionData.length < MAX_CONDITION_LIMIT && (
        <Grid item sm={12}>
          <Button
            onClick={handleAddAction}
            startIcon={<Add sx={{ fontSize: '20px' }} />}
            color="primary"
            sx={{
              p: '15px 1px',
              cursor: 'pointer',
              fontSize: '15px',
              '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
            }}
          >
            {strings.addCondition}
          </Button>
        </Grid>
      )}
    </Box>
  );
};
