import React from 'react';
import { Grid, TextField, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const RedirectToUrl = (props) => {
  const { action, index, validationData } = props;

  const { control, errors } = validationData;

  return (
    <>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.redirectUrlTooltip}</Typography>}
            >
              <span>
                {strings.redirectUrl}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={7} lg={9}>
          <Controller
            name={`actionData[${index}].redirectUrl`}
            control={control}
            mode="onBlur"
            defaultValue={action.redirectUrl}
            render={({ field: { onChange, value } }) => (
              <TextField
                id="redirectUrl"
                fullWidth
                size="small"
                type="text"
                value={value}
                placeholder={strings.redirectUrlPlaceholder}
                onChange={onChange}
                error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.redirectUrl?.message : false}
                helperText={errors?.actionData !== undefined ? errors?.actionData[index]?.redirectUrl?.message : ''}
              />
            )}
          />
        </Grid>
      </Grid>
    </>
  );
};
