import React, { useState } from 'react';
import { <PERSON>rid, <PERSON><PERSON><PERSON>, TextField, FormControl, Select, MenuItem, Switch, FormHelperText } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const Settings = (props) => {
  const { setEnableLogin, validationData } = props;
  const { register, errors, control, trigger, setValue, getValues } = validationData;

  return (
    <>
      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography id="name">
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.widgetNameTooltip}</Typography>}
            >
              <span>
                {strings.widgetName}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            name="name"
            id="name"
            variant="outlined"
            size="small"
            fullWidth
            {...register('name')}
            error={!!errors?.name}
            helperText={errors?.name?.message}
            placeholder={strings.widgetNamePlaceholder}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.widgetTitleTooltip}</Typography>}
            >
              <span>{strings.widgetTitle}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            {...register('widgetTitle')}
            fullWidth
            type="text"
            name="widgetTitle"
            autoComplete="off"
            size="small"
            error={!!errors?.widgetTitle}
            helperText={errors?.widgetTitle?.message}
            placeholder={strings.widgetTitlePlaceholder}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <CambianTooltip
            placement="right"
            title={<Typography variant="caption">{strings.consentRequiredTooltip}</Typography>}
          >
            <span>{strings.consent}</span>
          </CambianTooltip>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isConsentRequired"
            render={({ field: { ref, ...field } }) => (
              <Switch
                id="consent-toggle"
                size="small"
                checked={field?.value !== undefined ? field?.value : false}
                onChange={(event, val) => {
                  return field?.onChange(val);
                }}
              />
            )}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <CambianTooltip placement="right" title={<Typography variant="caption">{strings.signInTooltip}</Typography>}>
            <span>{strings.signIn}</span>
          </CambianTooltip>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="enableLogin"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  id="sign-in-toggle"
                  size="small"
                  checked={field?.value !== undefined ? field?.value : true}
                  onChange={(event, val) => {
                    setEnableLogin(val);
                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>
    </>
  );
};
