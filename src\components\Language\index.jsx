import React, { useState, useEffect, useCallback } from 'react';
import { FormControl, Select, MenuItem, Box } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { getWidgetLanguageParams } from '@/utils/commonUtility';
import { REGISTRATION_CAPS, QUESTIONNAIRE_CAPS, BOOKING_CAPS } from '@/utils/constants';
import { availableLanguages } from '@/utils/constants/common';
import { ConfirmationModal } from '@/components';

const langDropdownStyles = {
  select: {
    border: 'none',
    '& .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&:hover .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '.css-9ueuxu-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input': {
      paddingRight: '0 !important',
    },
  },
  icon: {
    fontSize: 28,
  },
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
  },
};

export const Language = ({
  SK,
  PK,
  widgetType,
  setValue,
  reset,
  strings,
  fetchWidgetCallback,
  getValues,
  watch,
  isFormDirty,
  onSaveBeforeLanguageChange,
  setLanguageChanged,
  onLanguageChange,
}) => {
  const [showLanguageChangeModal, setShowLanguageChangeModal] = useState(false);
  const [pendingLanguage, setPendingLanguage] = useState(null);
  useEffect(() => {
    const currentLanguage = watch('currentLanguage') || 'en';
    strings.setLanguage(currentLanguage);
    strings.setActiveLanguage(currentLanguage);
  }, [watch('currentLanguage'), strings]);

  const localizedLanguages = availableLanguages.map((lang) => ({
    value: lang.value,
    label: lang.value === 'en' ? strings.english || 'English' : strings.french || 'Français',
  }));
  const applyLanguageGlobally = (language) => {
    setValue('currentLanguage', language);
    strings.setLanguage(language);
    strings.setActiveLanguage(language);
  };

  const resetFieldsForLanguageChange = useCallback(
    (currentValues, newLanguage) => {
      if (widgetType.includes(QUESTIONNAIRE_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          nextButtonText: '',
          previousButtonText: '',
          doneButtonText: '',
          spinnerText: '',
          actionButtonText: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          dynamicIdErrorPage: { heading: '', description: '' },
          saveForLaterHeading: '',
          saveForLaterDescription: '',
          discardHeading: '',
          discardDescription: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      } else if (widgetType.includes(BOOKING_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          bookingSummaryDescription: '',
          actionButtonText: '',
          preConfirmationMessage: '',
          confirmationMessage: '',
          cancellationMessage: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          confirmationHeading: '',
          confirmationDescription: '',
          confirmationButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      } else if (widgetType.includes(REGISTRATION_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          actionButtonText: '',
          finalPageHeading: '',
          finalPageDescription: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      }
    },
    [widgetType, reset],
  );

  const handleWidgetLanguageChange = useCallback(
    (newLanguage, isUserInitiated = false) => {
      console.log('handleWidgetLanguageChange called:', { newLanguage, isUserInitiated, SK, PK });
      applyLanguageGlobally(newLanguage);

      // Check if widget was just saved (form values have SK/PK but props don't)
      const currentValues = getValues();
      const effectiveSK = SK || currentValues.SK;
      const effectivePK = PK || currentValues.PK;

      console.log('Effective SK/PK:', { effectiveSK, effectivePK, formSK: currentValues.SK, formPK: currentValues.PK });

      if (fetchWidgetCallback && effectiveSK && effectivePK) {
        const params = getWidgetLanguageParams({ SK: effectiveSK, PK: effectivePK, widgetType, language: newLanguage });
        console.log('Fetching widget with params:', params);

        if (params) {
          fetchWidgetCallback(params, (data) => {
            console.log('fetchWidgetCallback returned:', data);
            // Get fresh current values after potential form reset
            const freshCurrentValues = getValues();
            const currentWidgetName = freshCurrentValues.name;

            if (data) {
              console.log('Resetting form with fetched data for language:', newLanguage);
              reset({
                ...data,
                SK: effectiveSK,
                PK: effectivePK,
                currentLanguage: newLanguage,
                name: currentWidgetName || data.name,
              });
              return;
            }

            console.log('No data found for language, resetting fields to empty');
            if (isUserInitiated && setLanguageChanged) {
              setLanguageChanged(true);
            }
            // Reset fields with empty values for the new language using fresh values
            resetFieldsForLanguageChange(freshCurrentValues, newLanguage);
          });
        }
      } else {
        console.log('No SK/PK available, resetting fields for new widget');
        // For new widgets, also reset fields when language changes
        if (isUserInitiated && setLanguageChanged) {
          setLanguageChanged(true);
        }
        resetFieldsForLanguageChange(currentValues, newLanguage);
      }
    },
    [SK, PK, widgetType, fetchWidgetCallback, getValues, reset, setLanguageChanged, resetFieldsForLanguageChange],
  );
  useEffect(() => {
    onLanguageChange?.(handleWidgetLanguageChange);
  }, [onLanguageChange, handleWidgetLanguageChange]);

  const handleLanguageChange = (event) => {
    const newLanguage = event.target.value;
    const currentLanguage = getValues('currentLanguage');

    if (newLanguage !== currentLanguage) {
      if (isFormDirty) {
        setPendingLanguage(event.target.value);
        setShowLanguageChangeModal(true);
      } else {
        handleWidgetLanguageChange(newLanguage, true);
      }
    }
  };

  const handleConfirm = (clearPendingLanguage = true) => {
    if (pendingLanguage) {
      const newLanguage = pendingLanguage;
      handleWidgetLanguageChange(newLanguage, true);
      if (clearPendingLanguage) {
        setPendingLanguage(null);
      }
    }
    setShowLanguageChangeModal(false);
  };

  const handleSaveAndChangeLanguage = () => {
    setShowLanguageChangeModal(false);

    if (onSaveBeforeLanguageChange) {
      onSaveBeforeLanguageChange().then(() => {
        // Wait for the form to be updated with new SK/PK after widget save
        // and for any useEffect in widget containers to complete
        setTimeout(() => {
          console.log('Applying language change after widget save');
          const currentValues = getValues();
          console.log('Current form values after save:', {
            SK: currentValues.SK,
            PK: currentValues.PK,
            currentLanguage: currentValues.currentLanguage,
          });
          handleConfirm(false);
          setPendingLanguage(null);
        }, 500);
      });
    } else {
      handleConfirm(true);
    }
  };

  return (
    <>
      <FormControl variant="outlined" size="small">
        <Select
          id="current-language-dropdown"
          value={watch('currentLanguage') || 'en'}
          onChange={handleLanguageChange}
          displayEmpty
          inputProps={{ 'aria-label': 'language' }}
          IconComponent={() => null}
          sx={langDropdownStyles.select}
          renderValue={(selected) => (
            <Box sx={langDropdownStyles.flexCenter}>
              <Box component="span" sx={langDropdownStyles.flexCenter}>
                <LanguageIcon color="primary" sx={langDropdownStyles.icon} />
              </Box>
            </Box>
          )}
        >
          {localizedLanguages.map((lang) => (
            <MenuItem key={lang.value} value={lang.value}>
              {lang.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {showLanguageChangeModal && (
        <ConfirmationModal
          modalOpen={showLanguageChangeModal}
          handleClose={() => handleConfirm()}
          handleConfirm={handleSaveAndChangeLanguage}
          heading={strings.unsavedChanges}
          modalDescription={strings.unsavedChangesMessage}
          closeButtonText={strings.discard}
          confirmButtonText={strings.save}
          handleCloseIconClick={() => setShowLanguageChangeModal(false)}
        />
      )}
    </>
  );
};
