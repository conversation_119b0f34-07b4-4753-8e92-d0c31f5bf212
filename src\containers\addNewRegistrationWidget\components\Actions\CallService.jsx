import React, { useEffect } from 'react';
import { Grid, Typography, Select, MenuItem, FormHelperText, TextField, Switch } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const CallService = (props) => {
  const { callbackFetchGetSelectAction } = props;

  const {
    data: { index, item },
    actionFields,
    setActionFields,
    validationData,
  } = props;
  const { control, errors } = validationData;

  useEffect(() => {
    if (callbackFetchGetSelectAction) callbackFetchGetSelectAction();
  }, []);

  const handleServiceDropdown = (newValue, index) => {
    setActionFields((prevActionFields) => {
      const updatedData = [...prevActionFields];
      updatedData[index] = {
        ...updatedData[index],
        selectedService: newValue?.props?.value,
      };
      return updatedData;
    });
  };

  const handleBackgroundServiceUrl = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        backgroundServiceUrl: event.target.value,
      };
      return updatedData;
    });
  };

  const handleParameterToggle = (newValue, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        appendIncomingParameters: newValue?.props?.value,
      };
      return updatedData;
    });
  };

  return (
    <Grid container alignItems="center" rowGap={2} mt={1.5}>
      <Grid item xs={4} lg={3}>
        <Typography>
          <CambianTooltip
            placement="right"
            title={<Typography variant="caption">{strings.apiEndpointTooltip}</Typography>}
          >
            <span>
              {strings.apiEndpoint}
              {' *'}
            </span>
          </CambianTooltip>
        </Typography>
      </Grid>
      <Grid item xs={8} lg={9}>
        <Controller
          name={`actionData[${index}].backgroundServiceEndpoint`}
          control={control}
          mode="onChange"
          render={({ field }) => (
            <TextField
              size="small"
              fullWidth
              name={`actionData[${index}].backgroundServiceEndpoint`}
              value={field?.value}
              onChange={(event) => {
                handleBackgroundServiceUrl(event, index);
                return field?.onChange(event.target.value);
              }}
              placeholder={strings.apiEndpointPlaceholder}
              error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.backgroundServiceEndpoint : false}
              helperText={
                errors?.actionData !== undefined ? errors?.actionData[index]?.backgroundServiceEndpoint?.message : ''
              }
            />
          )}
        />
      </Grid>
    </Grid>
  );
};
