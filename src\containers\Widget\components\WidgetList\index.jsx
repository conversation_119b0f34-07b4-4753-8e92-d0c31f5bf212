import React, { useMemo, useRef, useState } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  TextField,
  Typography,
} from '@mui/material';
import { RowAction } from './RowAction';
import { extractGUID, formatDate } from '@/utils/commonUtility';
import { visuallyHidden } from '@mui/utils';
import strings from '@/utils/localization';
import { Close } from '@mui/icons-material';
import { useEffect } from 'react';

const columns = [
  { id: 'name', label: 'Name', minWidth: 300, align: 'left' },
  { id: 'updatedAt', label: 'Modified', minWidth: 150, align: 'left' },
  { id: 'type', label: 'Type', minWidth: 120, align: 'left' },
  { id: 'action', label: '', minWidth: 30, align: 'left' },
];

function createData(id, name, updatedAt, createdAt, type, action) {
  return { id, name, updatedAt, createdAt, type, action };
}

function descendingComparator(a, b, orderBy) {
  const varA = a[orderBy]?.toUpperCase();
  const varB = b[orderBy]?.toUpperCase();

  if (varB < varA) {
    return -1;
  }
  if (varB > varA) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

export const WidgetList = (props) => {
  const {
    organizationId,
    widgetBaseUrl,
    widgetsList,
    handleEditWidgetCallback,
    handlePreviewWidgetCallback,
    handleDuplicateWidgetCallback,
    handleDeleteWidgetCallback,
    activeVerificationWidgetId,
    handleExportWidgetCallback,
  } = props;

  const sortStateFromLocalStorage = JSON.parse(localStorage.getItem('sort'));

  const searchFieldRef = useRef(null);
  const [rows, setRows] = useState([]);
  const [sort, setSort] = useState(
    sortStateFromLocalStorage ? sortStateFromLocalStorage : { key: 'name', order: 'asc' },
  );

  useEffect(() => {
    if (!sortStateFromLocalStorage) localStorage.setItem('sort', JSON.stringify(sort));
  }, []);

  useMemo(() => {
    if (!widgetsList?.length) return;

    setRows(
      widgetsList.map((widget) =>
        createData(
          extractGUID(widget?.SK),
          widget?.name,
          widget?.updatedAt,
          widget?.createdAt,
          widget?.widgetType,
          <RowAction
            organizationId={organizationId}
            widgetBaseUrl={widgetBaseUrl}
            widgetData={widget}
            handleEditWidgetCallback={handleEditWidgetCallback}
            handlePreviewWidgetCallback={handlePreviewWidgetCallback}
            handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
            handleDeleteWidgetCallback={handleDeleteWidgetCallback}
            handleExportWidgetCallback={handleExportWidgetCallback}
          />,
        ),
      ),
    );
  }, [widgetsList]);

  const sortedRows = useMemo(() => {
    if (rows?.length) {
      return stableSort(rows, getComparator(sort.order, sort.key));
    }
  }, [rows, sort]);

  const handleRequestSort = (event, property) => {
    const isAsc = sort.key === property && sort.order === 'asc';
    // TODO: set sort key and sort order here
    localStorage.setItem('sort', JSON.stringify({ key: property, order: isAsc ? 'desc' : 'asc' }));
    setSort({ key: property, order: isAsc ? 'desc' : 'asc' });
  };

  const createSortHandler = (property) => (event) => {
    handleRequestSort(event, property);
  };

  const handleWidgetsSearch = (e) => {
    const searchString = e?.target?.value || '';
    const searchResult = widgetsList.filter(
      (widget) =>
        widget.name.toLowerCase().includes(searchString.toLowerCase()) ||
        widget.widgetType.toLowerCase().includes(searchString.toLowerCase()),
    );

    setRows(
      searchResult.map((widget) =>
        createData(
          extractGUID(widget?.SK),
          widget?.name,
          widget?.updatedAt,
          widget?.createdAt,
          widget?.widgetType,
          <RowAction
            organizationId={organizationId}
            widgetBaseUrl={widgetBaseUrl}
            widgetData={widget}
            handleEditWidgetCallback={handleEditWidgetCallback}
            handlePreviewWidgetCallback={handlePreviewWidgetCallback}
            handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
            handleDeleteWidgetCallback={handleDeleteWidgetCallback}
          />,
        ),
      ),
    );
  };

  const handleClearSearchKey = () => {
    handleWidgetsSearch();
    searchFieldRef.current.value = '';
  };

  const renderVerificationWidgetName = (label) => (
    <Typography variant="body2">
      {label}{' '}
      <Typography variant="body2" color="primary" component="span">
        {`[${strings.active}]`}
      </Typography>
    </Typography>
  );

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <TextField
          autoComplete="off"
          size="small"
          onChange={handleWidgetsSearch}
          placeholder={strings.search}
          inputRef={searchFieldRef}
          sx={{ width: { md: 250 }, maxWidth: '100%' }}
          InputProps={{
            endAdornment: searchFieldRef?.current?.value && (
              <Close onClick={handleClearSearchKey} sx={{ color: 'grey', cursor: 'pointer' }} />
            ),
          }}
        />
      </Box>
      {widgetsList?.length && rows?.length ? (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                {columns.map((column, index) => (
                  <TableCell
                    key={index}
                    align={column.align}
                    sortDirection={sort.key === column.id ? sort.order : false}
                  >
                    {column.id !== 'action' && (
                      <TableSortLabel
                        active={sort.key === column.id}
                        direction={sort.key === column.id ? sort.order : 'asc'}
                        onClick={createSortHandler(column.id)}
                      >
                        {column.label}
                        {sort.key === column.id ? (
                          <Box component="span" sx={visuallyHidden}>
                            {sort.order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                          </Box>
                        ) : null}
                      </TableSortLabel>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedRows?.length &&
                sortedRows.map((row, index) => (
                  <TableRow key={index}>
                    {columns.map((column, index) => (
                      <TableCell key={index} sx={{ width: { md: column.minWidth }, py: '0.8rem', px: 2 }}>
                        {['createdAt', 'updatedAt'].includes(column.id)
                          ? formatDate(row[column.id], 'yyyy-MM-dd')
                          : column.id === 'name' && row.type === 'VERIFICATION' && row.id === activeVerificationWidgetId
                          ? renderVerificationWidgetName(row[column.id])
                          : row[column.id]}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <>No Widgets found</>
      )}
    </Box>
  );
};
