export const ORGANI<PERSON>ATION_ID = 'ORGANIZATION_ID';
export const WIDGET_ID = 'WIDGET_ID';
export const BOOKING_WIDGET_ID = 'BOOKING_WIDGET_ID';
export const QUESTIONNAIRE_ID = 'QUESTIONNAIRE_ID';
export const QUESTIONNAIRE_WIDGET_ID = 'QUESTIONNAIRE_WIDGET_ID';
export const REGISTRATION_ID = 'REGISTRATION_ID';
export const REGISTRATION_WIDGET_ID = 'REGISTRATION_WIDGET_ID';
export const REGISTRY = 'REGISTRY';
export const BASE64_IMAGE = 'data:image/jpg;base64,';
export const EMAIL = 'EMAIL';
export const PHONE = 'MOBILE_PHONE_NUMBER';
export const PHN = 'PHN';
export const HEALTH_CARD = 'HEALTH_CARD';
export const COMPARE_IMAGE_URL = 'http://cambian.com/Location/location-image';
export const QUESTIONNAIRE = 'Questionnaire';
export const BOOKING = 'Booking';
export const REGISTRATION = 'Registration';
export const QUESTIONNAIRE_CAPS = 'QUESTIONNAIRE';
export const BOOKING_CAPS = 'BOOKING';
export const REGISTRATION_CAPS = 'REGISTRATION';
export const BOOKING_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/bookingWidget/${WIDGET_ID}`;
export const QUESTIONNAIRE_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/questionnaireWidget/${WIDGET_ID}`;
export const REGISTRATION_WIDGET_PREVIEW_URL = `/widget/organizations/${ORGANIZATION_ID}/registrationWidget/${WIDGET_ID}`;
export const CODE = 'code';
export const IDENTIFIED = 'IDENTIFIED';
export const DEIDENTIFIED = 'DEIDENTIFIED';
export const UNIDENTIFIED = 'UNIDENTIFIED';
export const CLINICAL = 'ORGANIZATION_USER';
export const PATIENT = 'INDIVIDUAL';
export const WIDGET_TYPE = 'WIDGET_TYPE';
export const IFRAME_CDN_LINK_NEW = '/iframeResizerNew.js';
export const SCORE_DEFINITIONS_URL = 'http://cambian.com/Questionnaire/list-of-score-definitions';
export const SCORE_NAME_URL = 'http://cambian.com/Questionnaire/score-name';
export const REMINDER_PREFERENCE = 'REMINDER_PREFERENCE';
export const COMMUNICATION = 'COMMUNICATION';
export const MAX_CONDITION_LIMIT = 6;

export const repositoryTypes = {
  public: 'PUBLIC',
  private: 'PRIVATE',
};
