import React, { useRef, useState } from 'react';
import { Box, Button, Stack, Typography } from '@mui/material';
import { KeyboardArrowDown } from '@mui/icons-material';
import { WidgetList } from './components/WidgetList';
import { WidgetTypeMenu } from './components/WidgetTypeMenu';
import { QUESTIONNAIRE_CAPS, BOOKING_CAPS } from '@/utils/constants';
import strings from '@/utils/localization';
import { HeaderWithActions, PanelBorder } from '@/components';

export const Widgets = (props) => {
  const {
    organizationId,
    widgetBaseUrl,
    widgetsList,
    handleEditWidgetCallback,
    handleNavigationCallback,
    handleImportWidgetCallback,
    handlePreviewWidgetCallback,
    handleDuplicateWidgetCallback,
    handleDeleteWidgetCallback,
    handleExportWidgetCallback,
  } = props;

  const [newWidgetTypeMenu, setNewWidgetTypeMenu] = useState(false);
  const importInputRef = useRef(null);

  const openNewWidgetTypeMenu = (event) => {
    setNewWidgetTypeMenu(event.currentTarget);
  };

  const closeNewWidgetTypeMenu = () => {
    setNewWidgetTypeMenu(null);
  };

  const handleAddNewWidget = (event) => {
    openNewWidgetTypeMenu(event);
  };

  const handleWidgetImport = (event) => {
    const widgetFile = event?.target?.files && event?.target?.files[0];

    const fileReader = new FileReader();
    fileReader.readAsText(widgetFile, 'UTF-8');
    fileReader.onload = (readerEvent) => {
      let widgetData = JSON.parse(readerEvent.target.result);

      widgetData.name = widgetData.name;

      delete widgetData.SK;
      delete widgetData.PK;

      if (widgetData.widgetType === BOOKING_CAPS) {
        widgetData.locations = [];
        widgetData.services = [];

        if (widgetData.action?.actionConditions?.length) {
          const processedActionConditions = widgetData.action.actionConditions.map((condition) => {
            return {
              ...condition,
              selectedServices: [],
            };
          });

          widgetData.action = {
            ...widgetData.action,
            actionConditions: processedActionConditions,
          };
        }
      } else if (widgetData.widgetType === QUESTIONNAIRE_CAPS) {
        widgetData.questionnaire = {
          artifactId: '',
          shortName: '',
        };
        if (widgetData.action?.metaData?.actionConditions?.length) {
          const processedActionConditions = widgetData.action.metaData.actionConditions.map((condition) => {
            return {
              ...condition,
              scoreDefinitionName: '',
            };
          });

          widgetData.action = {
            ...widgetData.action,
            metaData: {
              ...widgetData.action.metaData,
              actionConditions: processedActionConditions,
            },
          };
        } else {
          widgetData.action = {
            enabled: false,
            metaData: {},
          };
        }
      }

      handleImportWidgetCallback(widgetData, widgetData?.widgetType);
      importInputRef.current.value = null;
    };
  };

  return (
    <>
      <HeaderWithActions
        title={'Widgets'}
        actionButtons={[
          <Button component="label" variant="outlined">
            <input ref={importInputRef} type="file" onChange={handleWidgetImport} hidden />
            {strings.import}
          </Button>,
          <Button variant="contained" onClick={handleAddNewWidget} endIcon={<KeyboardArrowDown />}>
            New
          </Button>,
        ]}
      />
      <PanelBorder>
        <Box sx={{ p: 2 }}>
          <WidgetList
            organizationId={organizationId}
            widgetBaseUrl={widgetBaseUrl}
            widgetsList={widgetsList}
            handleEditWidgetCallback={handleEditWidgetCallback}
            handlePreviewWidgetCallback={handlePreviewWidgetCallback}
            handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
            handleDeleteWidgetCallback={handleDeleteWidgetCallback}
            handleExportWidgetCallback={handleExportWidgetCallback}
          />
        </Box>
      </PanelBorder>
      <WidgetTypeMenu
        anchorEl={newWidgetTypeMenu}
        handleClose={closeNewWidgetTypeMenu}
        handleNavigationCallback={handleNavigationCallback}
      />
    </>
  );
};
