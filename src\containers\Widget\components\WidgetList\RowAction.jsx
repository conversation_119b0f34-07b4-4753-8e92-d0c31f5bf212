import React, { useState } from 'react';
import { Code, Delete, Edit, FileCopy, InsertDriveFile, MoreVert, Visibility } from '@mui/icons-material';
import {
  Box,
  Button,
  Grid,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  Menu,
  MenuItem,
  Stack,
  TextField,
  TextareaAutosize,
} from '@mui/material';
import { ModalComponent, ConfirmationModal } from '@/components';
import { downloadFileInJsonFormat, extractGUID, getEmbedCode, getWidgetURL } from '@/utils/commonUtility';
import { BOOKING_CAPS, QUESTIONNAIRE_CAPS, REGISTRATION_CAPS } from '@/utils/constants';
import strings from '@/utils/localization';
import CopyToClipboard from 'react-copy-to-clipboard';
import { pages } from '@/utils/constants/common';

export const RowAction = (props) => {
  const {
    widgetData,
    widgetBaseUrl,
    organizationId,
    handleEditWidgetCallback,
    handlePreviewWidgetCallback,
    handleDuplicateWidgetCallback,
    handleDeleteWidgetCallback,
    handleExportWidgetCallback,
  } = props;

  const [anchorEl, setAnchorEl] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const widgetId = extractGUID(widgetData?.SK);

  const openMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const closeMenu = () => {
    setAnchorEl(null);
  };

  const openModal = () => {
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const openConfirmationModal = () => {
    setConfirmationModalOpen(true);
  };

  const closeConfirmationModal = () => {
    setConfirmationModalOpen(false);
  };

  const handleGetWidgetCode = () => {
    openModal();
    closeMenu();
  };

  const handlePreviewWidget = () => {
    handlePreviewWidgetCallback
      ? handlePreviewWidgetCallback(widgetId, widgetData?.widgetType)
      : console.log('preview handler not provided');
  };

  const exportWidget = () => {
    if (handleExportWidgetCallback) {
      handleExportWidgetCallback(widgetId, widgetData?.widgetType);
      closeMenu();
    } else {
      console.log('export handler not provided');
    }
  };

  const handleDuplicateWidget = () => {
    let updatedWidgetData = { ...widgetData };

    if (handleDuplicateWidgetCallback) {
      handleDuplicateWidgetCallback(updatedWidgetData, updatedWidgetData?.widgetType);
      closeMenu();
    } else {
      console.log('duplicate handler not provided');
    }
  };

  const handleDeleteWidget = () => {
    openConfirmationModal();
    closeMenu();
  };

  const handleEditWidget = () => {
    if (!handleEditWidgetCallback) {
      console.log('Edit widget handler is not provided');
      return;
    }
    if (widgetData?.widgetType === BOOKING_CAPS) {
      handleEditWidgetCallback(pages.addNewBookingWidget, widgetData);
    } else if (widgetData?.widgetType === QUESTIONNAIRE_CAPS) {
      handleEditWidgetCallback(pages.addNewQuestionnaireWidget, widgetData);
    } else if (widgetData?.widgetType === REGISTRATION_CAPS) {
      handleEditWidgetCallback(pages.addNewRegistrationWidget, widgetData);
    }
    closeMenu();
  };

  const handleConfirmDeleteWidget = () => {
    if (handleDeleteWidgetCallback) {
      handleDeleteWidgetCallback(widgetData, widgetData?.widgetType);
      closeConfirmationModal();
    } else {
      console.log('delete handler not provided');
    }
  };

  const getCodeSnippet = () => {
    let URL = '';
    let code = '';
    URL = getWidgetURL(widgetBaseUrl, organizationId, widgetId, widgetData?.widgetType, widgetData?.dynamicWidget);
    code = getEmbedCode(URL, widgetId, widgetBaseUrl);

    return (
      <Grid container rowGap={2}>
        <Grid item xs={12}>
          <TextField
            aria-label="code"
            value={code}
            multiline
            minRows={12}
            maxRows={12}
            inputProps={{ readOnly: true }}
            sx={{
              maxWidth: '100%',
              '& .MuiOutlinedInput-root': {
                maxWidth: '100% ',
              },
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Stack gap={2} direction="row" justifyContent="flex-end">
            <Button variant="outlined" id={strings.close} onClick={closeModal}>
              {strings.close}
            </Button>
            <CopyToClipboard text={code}>
              <Button variant="contained" id="copyToClipBoard" data-tip={strings.copied} data-event="click">
                {strings.copy}
              </Button>
            </CopyToClipboard>
          </Stack>
          {/* <Tooltip placement='top-start' leaveDelay={1000} />  */}
        </Grid>
      </Grid>
    );
  };

  const menuListOptions = [
    {
      id: 0,
      label: 'Get Code',
      icon: <Code />,
      onClick: handleGetWidgetCode,
    },
    {
      id: 1,
      label: 'Edit',
      icon: <Edit />,
      onClick: handleEditWidget,
    },
    {
      id: 2,
      label: 'Preview',
      icon: <Visibility />,
      onClick: handlePreviewWidget,
    },
    {
      id: 3,
      label: 'Duplicate',
      icon: <FileCopy />,
      onClick: handleDuplicateWidget,
    },
    {
      id: 4,
      label: 'Export',
      icon: <InsertDriveFile />,
      onClick: exportWidget,
    },
    {
      id: 5,
      label: 'Delete',
      icon: <Delete />,
      onClick: handleDeleteWidget,
    },
  ];

  return (
    <>
      <MoreVert sx={{ cursor: 'pointer', display: 'block' }} onClick={openMenu} />
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={closeMenu}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        {menuListOptions.map((listOption) => (
          <MenuItem key={listOption.id} onClick={listOption.onClick}>
            <ListItemIcon>{listOption.icon}</ListItemIcon>
            <ListItemText>{listOption.label}</ListItemText>
          </MenuItem>
        ))}
      </Menu>

      <ConfirmationModal
        modalOpen={confirmationModalOpen}
        heading={`Delete Widget`}
        modalDescription={`This action cannot be undone`}
        closeButtonText={strings.cancel}
        confirmButtonText={strings.delete}
        handleConfirm={handleConfirmDeleteWidget}
        handleClose={closeConfirmationModal}
      />
      <ModalComponent
        modalOpen={modalOpen}
        onClose={closeModal}
        mainContent={getCodeSnippet()}
        title={strings.iframeHeadingOne}
        width="md"
      />
    </>
  );
};
