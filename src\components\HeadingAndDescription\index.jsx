import React, { useEffect } from 'react';
import { Typo<PERSON>, Grid, TextField, Box } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const HeadingAndDescription = (props) => {
  const {
    headingName,
    descriptionName,
    buttonName,
    errors,
    control,
    headingError,
    descriptionError,
    buttonNameError,
    headingPlaceholder,
    descriptionPlaceholder,
    buttonNamePlaceholder,
    enableButton,
    required,
  } = props;

  let errorHeadingMessage = headingError?.message || '';
  let errorDescriptionMessage = descriptionError?.message || '';
  let errorButtonNameMessage = buttonNameError?.message || '';

  if (!headingError?.message && errors && errors[headingName] && errors[headingName]?.message) {
    errorHeadingMessage = errors[headingName]?.message;
  }
  if (!descriptionError?.message && errors && errors[descriptionName] && errors[descriptionName]?.message) {
    errorDescriptionMessage = errors[descriptionName]?.message;
  }
  if (!buttonNameError?.message && errors && errors[buttonName] && errors[buttonName]?.message) {
    errorButtonNameMessage = errors[buttonName]?.message;
  }

  return (
    <Box>
      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <span>
            {strings.heading}
            {required && ' *'}
          </span>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name={headingName}
            render={({ field: { ref, ...field } }) => (
              <TextField
                {...field}
                fullWidth
                size="small"
                placeholder={headingPlaceholder}
                type="text"
                name={headingName}
                onChange={(event) => {
                  if (event.target.value.trim()) {
                    field.onChange(event.target.value);
                  } else {
                    field.onChange(event.target.value.trim());
                  }
                }}
                error={!!errorHeadingMessage}
                helperText={errorHeadingMessage}
                autoComplete="off"
              />
            )}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <span>
            {strings.description}
            {required && ' *'}
          </span>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name={descriptionName}
            render={({ field: { ref, ...field } }) => (
              <TextField
                {...field}
                fullWidth
                size="small"
                multiline
                minRows={6}
                placeholder={descriptionPlaceholder}
                type="text"
                name={descriptionName}
                // {...register(descriptionName)}
                onChange={(event) => {
                  if (event.target.value.trim()) {
                    field.onChange(event.target.value);
                  } else {
                    field.onChange(event.target.value.trim());
                  }
                }}
                error={!!errorDescriptionMessage}
                helperText={errorDescriptionMessage}
                autoComplete="off"
              />
            )}
          />
        </Grid>
      </Grid>
      {enableButton && (
        <Grid container alignItems="center" mt={2}>
          <Grid item xs={4} lg={3}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.buttonTooltip}</Typography>}
            >
              <span>{strings.button}</span>
            </CambianTooltip>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              control={control}
              name={buttonName}
              render={({ field: { ref, ...field } }) => (
                <TextField
                  {...field}
                  fullWidth
                  size="small"
                  placeholder={buttonNamePlaceholder}
                  type="text"
                  name={buttonName}
                  onChange={(event) => {
                    if (event.target.value.trim()) {
                      field.onChange(event.target.value);
                    } else {
                      field.onChange(event.target.value.trim());
                    }
                  }}
                  error={!!errorButtonNameMessage}
                  helperText={errorButtonNameMessage}
                  autoComplete="off"
                />
              )}
            />
          </Grid>
        </Grid>
      )}
    </Box>
  );
};
