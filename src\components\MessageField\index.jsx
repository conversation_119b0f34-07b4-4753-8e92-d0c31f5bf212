import React from 'react';
import { Grid, Typography, TextField } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';

export const MessageField = ({ id, label, placeholder, name, control, errors }) => (
  <Grid container sx={{ my: 2 }}>
    <Grid item xs={4} lg={3}>
      <Typography>
        <span>{label}</span>
      </Typography>
    </Grid>
    <Grid item xs={8} lg={9}>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <TextField
            id={id}
            size="small"
            fullWidth
            multiline
            minRows={2}
            placeholder={placeholder}
            value={value || ''}
            onChange={(event) => {
              const trimmedValue = event.target.value.trim();
              onChange(event.target.value ? event.target.value : trimmedValue);
            }}
            error={!!errors?.[name]}
            helperText={errors?.[name]?.message || ''}
          />
        )}
      />
    </Grid>
  </Grid>
);
