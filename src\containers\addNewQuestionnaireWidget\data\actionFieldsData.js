import strings from '@/utils/localization';

export const actionFieldsData = {
  displayScore: strings.displayScoreIs,
  selectScore: [],
  scoreDropDown: [
    {
      id: 0,
      label: strings.scoreEqualTo,
      value: '=',
    },
    {
      id: 1,
      label: strings.scoreGreaterThan,
      value: '>',
    },
    {
      id: 2,
      label: strings.scoreGreaterThanOrEqual,
      value: '>=',
    },
    {
      id: 3,
      label: strings.scoreLessThan,
      value: '<',
    },
    {
      id: 4,
      label: strings.scoreLessThanOrEqual,
      value: '<=',
    },
    {
      id: 5,
      label: strings.scoreBetween,
      value: 'between',
    },
  ],
  actionDropDown: [
    {
      id: 0,
      label: strings.actionPage,
      value: 'Page',
    },
    {
      id: 1,
      label: strings.actionURL,
      value: 'URL',
    },
    {
      id: 2,
      label: strings.actionWidget,
      value: 'Widget',
    },
    {
      id: 3,
      label: strings.actionService,
      value: 'Service',
    },
  ],
  targetDropDown: [
    {
      id: 0,
      label: strings.targetNewTab,
      value: 'Open in new Tab',
    },
    {
      id: 1,
      label: strings.targetSameTab,
      value: 'Open in same Tab',
    },
    {
      id: 2,
      label: strings.targetNewWindow,
      value: 'Open in new Window',
    },
    {
      id: 3,
      label: strings.targetSameIFrame,
      value: 'Open in same IFrame',
    },
  ],
  widgetTypes: [
    {
      id: 0,
      label: strings.bookingWidgetType,
      value: 'Booking Widget',
    },
    {
      id: 1,
      label: strings.questionnaireWidgetType,
      value: 'Questionnaire Widget',
    },
    {
      id: 2,
      label: strings.registrationWidgetType,
      value: 'Registration Widget',
    },
  ],
  scoreDefinitionName: '',
  selectedDropdownScore: '',
  selectedScore: '',
  selectedAction: '',
  selectedTarget: '',
  selectedWidgetType: '',
  selectedWidget: { name: '', id: null },
  serviceDropdown: [
    {
      id: 0,
      label: strings.serviceCallBackgroundURL,
      value: 'Call Background URL',
    },
  ],
  selectedService: '',
  backgroundServiceEndpoint: '',
  redirectUrl: '',
  appendIncomingParameters: true,
  demographicPageShow: false,
  demographicFieldsAdd: true,
  default: true,
};
