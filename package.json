{"name": "@appscoopsolutions/cambianwe", "version": "0.0.28", "publishConfig": {"registry": "https://npm.pkg.github.com/appscoopsolutions"}, "type": "module", "engines": {"node": ">=20.0.0", "npm": ">=8.0.0", "yarn": ">=1.22.18"}, "scripts": {"rollup": "rollup -c", "dev": "vite", "build-aws": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "peerDependencies": {"@appscoopsolutions/component-ui": "0.0.46", "@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@mui/icons-material": "^5.2.5", "@mui/material": "^5.15.1", "@mui/styles": "^5.2.5", "@mui/utils": "^5.16.14", "date-fns": "^3.6.0", "google-maps-react": "^2.0.6", "notistack": "^3.0.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "yup": "^1.4.0", "crypto-js": "^4.2.0"}, "devDependencies": {"@appscoopsolutions/component-ui": "0.0.46", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.2.5", "@mui/material": "^5.15.1", "@mui/styles": "^5.2.5", "@mui/utils": "^5.16.14", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^15.2.3", "@vitejs/plugin-react": "^4.2.1", "date-fns": "^3.6.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "google-maps-react": "^2.0.6", "notistack": "^3.0.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-localization": "^1.0.19", "rollup": "^2.79.1", "rollup-plugin-css-only": "^4.5.2", "vite": "^5.1.6", "yup": "^1.4.0", "crypto-js": "^4.2.0"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"], "dependencies": {}}