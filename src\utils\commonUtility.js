import {
  BOOKING_CAPS,
  BOOKING_WIDGET_PREVIEW_URL,
  CODE,
  EMAIL,
  IFRAME_CDN_LINK_NEW,
  ORGANIZATION_ID,
  QUESTIONNAIRE_CAPS,
  QUESTIONNAIRE_WIDGET_PREVIEW_URL,
  R<PERSON><PERSON>TRATION_CAPS,
  REGISTRATION_WIDGET_PREVIEW_URL,
  WIDGET_ID,
} from './constants';
import { format } from 'date-fns';

const createUUID = () => {
  let dt = new Date().getTime();
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = (dt + Math.random() * 16) % 16 | 0;
    dt = Math.floor(dt / 16);
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
};

export const getWidgetURL = (BASE_URL, organizationId, widgetId, widgetType, isDynamicWidget) => {
  let URL = '';
  const WIDGET_BASE_URL = BASE_URL;

  switch (widgetType) {
    case QUESTIONNAIRE_CAPS:
      URL = `${WIDGET_BASE_URL}${QUESTIONNAIRE_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      if (isDynamicWidget) URL += `?qid=`;
      return URL;

    case BOOKING_CAPS:
      URL = `${WIDGET_BASE_URL}${BOOKING_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      return URL;

    case REGISTRATION_CAPS:
      URL = `${WIDGET_BASE_URL}${REGISTRATION_WIDGET_PREVIEW_URL.replace(ORGANIZATION_ID, organizationId).replace(
        WIDGET_ID,
        widgetId,
      )}`;
      return URL;

    default:
      break;
  }
};

export const getEmbedCode = (src, widgetId, BASE_URL) => {
  const uuid = createUUID();
  const code = `
      <div>
        <div class='op-interactive' id='${
          widgetId || uuid
        }' data-title='${widgetId}' data-url='${src}' data-width='100%'></div>
        <script src="${BASE_URL}${IFRAME_CDN_LINK_NEW}"></script>
        <script>
          (function () {
            initIframe('${widgetId || uuid}')
          })()
          window.addEventListener('message', function(event) {
            if (event.data === "ScrollIntoView") {
              document.getElementById('${widgetId || uuid}').scrollIntoView({ behavior: 'smooth' });
            }
          });
        </script>
      </div>
    `;
  return code;
};

export const formatDate = (date, dateFormat) => {
  if (!date || !dateFormat) return '';
  return format(new Date(date), dateFormat);
};

export const downloadFileInJsonFormat = (jsonData, fileName) => {
  const blob = new Blob([jsonData], { type: 'application/json' });
  const href = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = href;
  link.download = fileName + '.json';
  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

export const getAvailableFieldsList = (availableFields, existingFields) => {
  let fields = [];
  if (!availableFields?.length) return fields;

  for (let availableField of availableFields) {
    let field = {
      code: availableField.code,
      SK: availableField.SK,
      display: existingFields.find((field) => availableField.SK === field.SK)?.display || availableField.display,
      position: existingFields?.length
        ? existingFields.find((field) => availableField.SK === field.SK)?.position || existingFields.length + 1
        : availableField.defaultPosition,
      systemRequired: availableField.systemRequired,
      systemMandatory: availableField.systemMandatory,
      match: availableField.match,
      checked:
        (availableField.systemRequired && availableField.systemMandatory) ||
        (existingFields?.length
          ? existingFields.find((item) => item.SK === availableField.SK)?.checked ?? false
          : false),
      systemAllowMultiple: availableField.systemAllowMultiple,
      allowMultiple:
        (availableField.systemMandatory && availableField.systemAllowMultiple) ||
        (existingFields?.length
          ? existingFields.find((item) => item.SK === availableField.SK)?.allowMultiple ?? false
          : false),
      isMandatory:
        availableField.systemMandatory ||
        (existingFields?.length
          ? existingFields.find((item) => item.SK === availableField.SK)?.isMandatory ?? false
          : false),
    };

    fields.push(field);
  }

  fields.sort((a, b) => (a.position < b.position ? -1 : a.position > b.position ? 1 : 0));

  return fields;
};

export const generateFieldsList = (widgetFields, availableFields, selectedIdTypes) => {
  const mapFields = (fields) =>
    fields.map((field) => ({
      ...field,
      checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
      isMandatory: field.required || field.isMandatory || false,
      systemRequired: field.systemRequired,
      systemMandatory: field.systemMandatory,
      match: field.match,
      position: field.defaultPosition,
      allowMultiple: field.allowMultiple || (field.systemMandatory && field.systemAllowMultiple) || false,
      display: field.display || field.name,
      ...(field.code === 'IDENTIFICATION' && { idTypes: selectedIdTypes }),
    }));

  const allSystemFields = mapFields(widgetFields || []);

  if (availableFields?.length) {
    return allSystemFields.map((systemField) => {
      const existingField = availableFields.find((f) => f.code === systemField.code);
      if (existingField) {
        return {
          ...systemField,
          ...existingField,
          checked: existingField.checked || (systemField.systemRequired && systemField.systemMandatory) || false,
          isMandatory: existingField.isMandatory || systemField.isMandatory || false,
          systemRequired: systemField.systemRequired,
          systemMandatory: systemField.systemMandatory,
          match: systemField.match,
          allowMultiple:
            existingField.allowMultiple || (systemField.systemMandatory && systemField.systemAllowMultiple) || false,
          display: existingField.display || systemField.display || systemField.name,
          ...(systemField.code === 'IDENTIFICATION' && { idTypes: selectedIdTypes }),
        };
      }
      return systemField;
    });
  }

  return allSystemFields;
};

export const extractGUID = (inputString) => {
  // * expected inputString format "SOME_PREFIX#GUID"
  if (!inputString) return '';
  const parts = inputString.split('#');

  if (parts.length >= 2) {
    return parts[1];
  } else {
    return null;
  }
};

export const sortLists = (x, y) => {
  if (x?.name?.toLowerCase() < y?.name?.toLowerCase()) {
    return -1;
  }
  if (x?.name?.toLowerCase() > y?.name?.toLowerCase()) {
    return 1;
  }
  return 0;
};

export const getWidgetLanguageParams = ({ SK, PK, widgetType, language }) => {
  if (!SK || !PK || !widgetType || !language) {
    return null;
  }

  const organizationId = extractGUID(PK);
  const widgetId = extractGUID(SK);

  return {
    organizationId,
    widgetId,
    widgetType: `${widgetType.toLowerCase()}Widget`,
    language,
  };
};
