import * as yup from 'yup';
import { EMAIL, PHONE } from '@/utils/constants';
import strings from '@/utils/localization';
const getMessage = (key) => () => strings[key];

export const schema = yup.object({
  name: yup.string().required(getMessage('widgetNameRequired')).min(2, getMessage('minCharLength2')),
  locations: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string(),
      }),
    )
    .min(1, getMessage('locationsRequired')),
  services: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string(),
      }),
    )
    .min(1, getMessage('servicesRequired')),
  defaultLanguage: yup.string().required(getMessage('defaultLanguageRequired')),
  identification: yup.string().required(getMessage('identificationRequired')),
  mapPlaceholderImage: yup
    .mixed()
    // .required('Please upload a file')
    .test('fileSize', getMessage('fileSizeTooLarge'), (value) => {
      return !value?.filename || value.size <= 2 * 1024 * 1024; // 2MB
    })
    .test('fileType', getMessage('unsupportedFileType'), (value) => {
      return !value?.filename || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(value.type);
    }),
  locationRequired: yup.boolean(),
  isOtpVerificationChecked: yup.boolean(),
  isDisplayEligibility: yup.boolean(),
  isIntroChecked: yup.boolean(),
  introHeading: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('headingRequired')).min(2, getMessage('minCharLength2')),
  }),
  introDescription: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('descriptionRequired')).min(2, getMessage('minCharLength2')),
  }),
  isConfirmationChecked: yup.boolean(),
  confirmationHeading: yup.string().when('isConfirmationChecked', {
    is: true,
    then: () => yup.string(),
  }),
  confirmationDescription: yup.string().when('isConfirmationChecked', {
    is: true,
    then: () => yup.string(),
  }),
  isMultipleIndividualChecked: yup.boolean(),
  isActionEnabled: yup.boolean(),
  actionButtonText: yup.string(),
  actionData: yup.array().when('isActionEnabled', {
    is: true,
    then: () =>
      yup.array(
        yup.object({
          selectedAction: yup.string().required(getMessage('actionRequired')),
          selectedServices: yup.array().when(['default'], {
            is: (defaultAction) => !defaultAction,
            then: () =>
              yup
                .array()
                .of(
                  yup.object().shape({
                    name: yup.string(),
                  }),
                )
                .min(1, getMessage('servicesRequired')),
            otherwise: () => yup.array().nullable(),
          }),
          actionHeading: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('headingRequired')),
          }),
          actionDescription: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('descriptionRequired')),
          }),
          backgroundServiceEndpoint: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Service',
            then: () => yup.string().url(getMessage('apiEndpointValid')).required(getMessage('apiEndpointRequired')),
          }),
          redirectUrl: yup.string().when('selectedAction', {
            is: (value) => value && value === 'URL',
            then: () => yup.string().url(getMessage('validURL')).required(getMessage('redirectUrlRequired')),
          }),
          selectedTarget: yup.string().when('selectedAction', {
            is: (value) => value && (value === 'URL' || value === 'Widget'),
            then: () => yup.string().required(getMessage('targetRequired')),
          }),
          selectedWidgetType: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Widget',
            then: () => yup.string().required(getMessage('widgetTypeRequired')),
          }),
          selectedWidget: yup
            .object()
            .shape()
            .when('selectedAction', {
              is: (value) => value && value === 'Widget',
              then: () =>
                yup.object({
                  name: yup.string().required(getMessage('widgetRequired')),
                }),
            }),
        }),
      ),
  }),
});

export const fieldsValidation = yup.array().of(
  yup.object().shape({
    code: yup.string().required(),
  }),
);
