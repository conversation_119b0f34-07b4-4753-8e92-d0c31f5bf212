input[type=color] {
	width: 42px;
	height: 40px; 
	border-radius: 50%;
	overflow: hidden;
	border: none;
	cursor: pointer;
}
@media (max-width: 426px) {
	input[type=color] {
		width: 35px;
		height: 33px;
	}
	.colorHeadings {
		font-size: 0.7rem !important;
	}
}
@media (max-width: 376px) {
	.colorHeadings {
		font-size: 0.6rem !important;
	}
}

input[type=color]::-webkit-color-swatch {
border: none;
border-radius: 50%;
padding: 0;
}

input[type=color]::-webkit-color-swatch-wrapper {
	border: none;
	border-radius: 50%;
	padding: 0;
}
.colorHeadings {
	margin-top: 0 !important;
}