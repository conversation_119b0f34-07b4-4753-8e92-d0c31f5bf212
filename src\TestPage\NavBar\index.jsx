import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>po<PERSON>, IconButton, MenuItem, <PERSON>u, Stack } from '@mui/material';
import AccountCircle from '@mui/icons-material/AccountCircle';
import { makeStyles } from '@mui/styles';
import strings from '@/utils/localization';
import CoordinatorLogo from '@/assets/images/Cambian_Coordinator_logo.png';

const useStyles = makeStyles((theme) => ({
  navColor: {
    backgroundColor: '#FFFFFF!important',
    margin: 0,
    borderBottom: 'solid  1px',
    borderBottomColor: theme.palette.divider,
  },
  paper: {
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
  },
  root: {
    padding: '0 48px 48px 48px',
  },
}));

const Branding = () => {
  return (
    <Stack direction="row">
      {/* <Box>
        <svg xmlns="http://www.w3.org/2000/svg"
            width="65" height="65"
            viewBox="0 0 400 473">
          <path id="Selection #2"
                fill="#4D76A9" stroke="#41638E" strokeWidth="1"
                d="M 337.00,376.00
                  C 333.05,380.51 328.28,382.61 323.00,385.25
                    312.82,390.34 302.04,394.54 291.00,397.37
                    240.53,410.30 187.94,399.08 147.00,366.79
                    93.45,324.56 72.93,251.28 94.35,187.00
                    101.53,165.47 113.36,146.02 128.29,129.00
                    138.58,117.26 153.43,105.64 167.00,97.85
                    188.36,85.59 217.19,76.04 242.00,76.00
                    242.00,76.00 258.00,76.00 258.00,76.00
                    283.83,76.31 314.23,86.87 336.00,100.42
                    344.07,105.45 354.45,112.32 360.00,120.00
                    360.00,120.00 333.00,123.16 333.00,123.16
                    333.00,123.16 310.00,126.42 310.00,126.42
                    268.37,132.89 224.80,143.68 188.00,164.72
                    159.56,180.98 134.59,204.00 135.00,239.00
                    135.31,264.78 155.64,289.99 174.00,306.15
                    206.51,334.78 240.87,352.37 282.00,365.02
                    296.48,369.48 322.10,375.32 337.00,376.00 Z
                  M 377.96,141.74
                  C 384.50,143.55 391.59,157.89 394.74,164.00
                    407.28,188.30 413.32,215.73 413.00,243.00
                    412.84,256.01 410.00,272.52 406.28,285.00
                    405.15,288.80 403.66,297.42 398.89,297.70
                    398.89,297.70 386.00,295.61 386.00,295.61
                    386.00,295.61 361.00,290.60 361.00,290.60
                    327.63,283.87 283.20,270.59 253.00,255.24
                    238.32,247.78 216.46,234.39 216.01,216.00
                    215.53,195.87 233.98,184.78 250.00,176.75
                    276.44,163.50 306.23,155.93 335.00,149.58
                    335.00,149.58 377.96,141.74 377.96,141.74 Z" />
        </svg>
        </Box>
        <Stack direction="column" justifyContent="center" alignItems="flex-start">
            <Box>
              <Typography variant={'body1'} sx={{color:'text.primary', lineHeight: 1}} >Cambian</Typography>
            </Box>
            <Box>
              <Typography variant={'h5'} sx={{color:'text.secondary', lineHeight: 1}} ></Typography>
            </Box>
        </Stack> */}
      <img src={CoordinatorLogo} width="190" height="54" alt="cambian" />
    </Stack>
  );
};

export const NavBar = () => {
  const classes = useStyles();

  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <AppBar position="fixed" className={classes.navColor} elevation={0}>
        <Toolbar>
          <Branding />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}></Typography>
          <div>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="primary"
            >
              <Stack direction="row" spacing={2} alignItems="center" justifyContent="center">
                <Typography variant="subtitle1" component="div" sx={{ lineHeight: 1 }}>
                  App-Scoop
                </Typography>
                <AccountCircle sx={{ m: 0, p: 0 }} />
              </Stack>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              keepMounted
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'center',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={handleClose}>App-Scoop</MenuItem>
              <MenuItem onClick={() => signOut()}>{strings.signOut}</MenuItem>
            </Menu>
          </div>
        </Toolbar>
      </AppBar>
      <Toolbar />
    </>
  );
};
