import React, { useEffect } from 'react';
import { Grid, FormControlLabel, Checkbox, Box, Autocomplete, TextField, Typography } from '@mui/material';
import strings from '@/utils/localization';

export const HealthCareIdTypes = (props) => {
  const { onIdTypeChange, selectedIdTypes = [], setSelectedIdTypes, allIdTypes, orgRequiredIdTypes = [] } = props;

  const handleIdTypeChange = (e, idType) => {
    let newSelectedIdTypes;
    if (e.target.checked) {
      const requiredIssuers =
        orgRequiredIdTypes
          .find((orgType) => orgType.idType === idType.idType)
          ?.issuers.filter((issuer) => issuer.required === true) || [];

      newSelectedIdTypes = [...selectedIdTypes, { idType: idType.idType, issuers: requiredIssuers, required: false }];
    } else {
      newSelectedIdTypes = selectedIdTypes.filter((item) => item.idType !== idType.idType);
    }
    setSelectedIdTypes(newSelectedIdTypes);
    onIdTypeChange(newSelectedIdTypes);
  };

  const isIdTypeDisabled = (idType) => {
    const orgType = orgRequiredIdTypes.find((orgType) => orgType.idType === idType.idType);
    return orgType?.required === true;
  };

  const isIssuerDisabled = (idType, issuer) => {
    const orgType = orgRequiredIdTypes.find((orgType) => orgType.idType === idType.idType);
    return orgType?.issuers.some((orgIssuer) => orgIssuer.issuer === issuer.issuer && orgIssuer.required === true);
  };

  const handleIssuerChange = (event, newValue, idType) => {
    const orgType = orgRequiredIdTypes.find((orgType) => orgType.idType === idType.idType);
    const requiredIssuers = orgType?.issuers.filter((issuer) => issuer.required === true) || [];

    const isSelectAllSelected = newValue.some((option) => option.issuer === strings.selectAll);
    const allIssuers = idType.issuers || [];

    const updatedIssuers = isSelectAllSelected
      ? [...allIssuers]
      : newValue.filter((option) => option.issuer !== strings.selectAll);

    const finalIssuers = [
      ...requiredIssuers,
      ...updatedIssuers.filter(
        (issuer) => !requiredIssuers.some((requiredIssuer) => requiredIssuer.issuer === issuer.issuer),
      ),
    ];

    const newSelectedIdTypes = selectedIdTypes.map((item) =>
      item.idType === idType.idType ? { ...item, issuers: finalIssuers } : item,
    );

    setSelectedIdTypes(newSelectedIdTypes);
    onIdTypeChange(newSelectedIdTypes);
  };

  const handleMandatoryChange = (e, idType) => {
    const newSelectedIdTypes = selectedIdTypes.map((item) =>
      item.idType === idType.idType
        ? {
            ...item,
            required: e.target.checked,
          }
        : item,
    );
    setSelectedIdTypes(newSelectedIdTypes);
    onIdTypeChange(newSelectedIdTypes);
  };

  useEffect(() => {
    const updatedSelectedIdTypes = selectedIdTypes.map((idType) => {
      const matchingIdType = allIdTypes.find((data) => data.idType === idType.idType);
      if (matchingIdType) {
        const orgType = orgRequiredIdTypes.find((orgType) => orgType.idType === idType.idType);
        const requiredIssuers = orgType?.issuers.filter((issuer) => issuer.required === true) || [];

        const updatedIssuers = idType.issuers.map((issuer) => {
          const matchingIssuer = matchingIdType.issuers.find((dataIssuer) => dataIssuer.issuer === issuer.issuer);
          return {
            ...issuer,
            displayName: matchingIssuer?.displayName || issuer.displayName,
          };
        });

        return {
          ...idType,
          issuers: [
            ...requiredIssuers,
            ...updatedIssuers.filter(
              (issuer) => !requiredIssuers.some((requiredIssuer) => requiredIssuer.issuer === issuer.issuer),
            ),
          ],
        };
      }
      return idType;
    });

    const isUpdated = JSON.stringify(updatedSelectedIdTypes) !== JSON.stringify(selectedIdTypes);
    if (isUpdated) {
      setSelectedIdTypes(updatedSelectedIdTypes);
      onIdTypeChange(updatedSelectedIdTypes);
    }
  }, [allIdTypes, selectedIdTypes]);

  return (
    <Box>
      {allIdTypes.map((idType) => (
        <Grid container alignItems="center" key={idType.idType}>
          <Grid item xs={7} sm={6} md={2} sx={{ display: 'flex', alignItems: 'center', marginLeft: '10px' }}>
            <Typography variant="body2">{idType.idType}</Typography>
          </Grid>
          <Grid item xs={7} sm={6} md={4.4} sx={{ display: 'flex', alignItems: 'center' }}>
            {selectedIdTypes.find((item) => item.idType === idType.idType) && (
              <Autocomplete
                multiple
                limitTags={2}
                id="multiple-limit-tags"
                size="small"
                options={[{ issuer: strings.selectAll }, ...idType.issuers]}
                value={selectedIdTypes.find((item) => item.idType === idType.idType)?.issuers || []}
                getOptionLabel={(option) => option.displayName || option.issuer}
                isOptionEqualToValue={(option, value) => option.issuer === value.issuer}
                disableCloseOnSelect
                onChange={(event, newValue) => handleIssuerChange(event, newValue, idType)}
                renderOption={(props, option) => {
                  const idTypeObj = selectedIdTypes.find((item) => item.idType === idType.idType);
                  const isSelectAllOption = option.issuer === strings.selectAll;
                  const isDisabled = isIssuerDisabled(idType, option);
                  const isPrechecked = orgRequiredIdTypes
                    .find((orgType) => orgType.idType === idType.idType)
                    ?.issuers.some((orgIssuer) => orgIssuer.issuer === option.issuer && orgIssuer.required === true);

                  const allIssuersSelected = idTypeObj?.issuers.length === idType.issuers.length;

                  return (
                    <li {...props} aria-disabled={isDisabled}>
                      <Checkbox
                        id={`${idType.idType}-${option.issuer}-issuer`}
                        style={{ marginRight: 8 }}
                        checked={
                          isSelectAllOption
                            ? allIssuersSelected
                            : isPrechecked || idTypeObj?.issuers.some((issuer) => issuer.issuer === option.issuer)
                        }
                        disabled={isSelectAllOption ? false : isDisabled}
                      />
                      {option.displayName || option.issuer}
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    label={strings.selectIssuers}
                    placeholder={strings.selectIssuersPlaceholder}
                    style={{ width: '390px', marginTop: '10px' }}
                  />
                )}
                clearOnEscape={false}
              />
            )}
          </Grid>
          <Grid item xs={3} md={1} sx={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              id={`${idType.idType}-idtype`}
              checked={Boolean(selectedIdTypes.find((item) => item.idType === idType.idType))}
              onChange={(e) => handleIdTypeChange(e, idType)}
              disabled={isIdTypeDisabled(idType)}
              sx={{ ml: 1 }}
            />
          </Grid>
        </Grid>
      ))}
    </Box>
  );
};
