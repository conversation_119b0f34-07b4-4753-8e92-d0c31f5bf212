import React, { useEffect, useState } from 'react';
// import { WidgetEditor } from '@appscoopsolutions/cambianwe';
import { WidgetEditor } from '@/containers/WidgetEditor';

import {
  BOOKING_CAPS,
  BOOKING_WIDGET_ID,
  <PERSON><PERSON><PERSON><PERSON>ATION_ID,
  QUESTIONNAIRE_CAPS,
  QUEST<PERSON>NAIRE_ID,
  QUESTIONNAIRE_WIDGET_ID,
  R<PERSON><PERSON><PERSON>ATION_CAPS,
  REGISTRATION_WIDGET_ID,
  repositoryTypes,
  WIDGET_ID,
  WIDGET_TYPE,
} from '@/utils/constants';
import {
  BOOKING_WIDGET_ENDPOINT,
  CREATE_BOOKING_WIDGET,
  CREATE_QUESTIONNAIRE_WIDGET,
  CREATE_REGISTRATION_WIDGET,
  DELETE_WIDGET,
  GET_ALL_BOOKING_WIDGET,
  GET_ALL_QUESTIONNAIRE_WIDGET,
  GET_ALL_REGISTRATION_WIDGET,
  GET_LOCATIONS_LIST,
  GET_ORGANIZATION_DETAILS,
  GET_REGISTRATION_WIDGET,
  GET_SERVICES_LIST,
  IMPORT_QUESTIONNAIRE_WIDGET,
  QUESTIONNAIRE_WIDGET_ENDPOINT,
  NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL,
  NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL,
  NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL,
  NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL,
  IMPORT_BOOKING_WIDGET,
} from './awsApiEndpoints';
import { mergeAndSortWidgets } from '@/containers/Widget/widgetUtility';
import { downloadFileInJsonFormat, extractGUID, getWidgetURL } from '@/utils/commonUtility';
import { Loader } from '@/components';
import { MD5 } from 'crypto-js';
import { Box } from '@mui/material';

const organizationAllFields = [
  {
    SK: 0,
    systemRequired: false,
    defaultPosition: 1,
    code: 'FIRST_NAME',
    display: 'First Name',
    allowMultiple: false,
  },
  {
    SK: 1,
    systemRequired: false,
    defaultPosition: 2,
    code: 'MIDDLE_NAME',
    display: 'Middle Name',
    allowMultiple: false,
  },
  {
    SK: 2,
    systemRequired: false,
    defaultPosition: 3,
    code: 'LAST_NAME',
    display: 'Last Name',
    allowMultiple: false,
  },
  {
    SK: 3,
    systemRequired: false,
    defaultPosition: 4,
    code: 'DATE_OF_BIRTH',
    display: 'Date of Birth',
    allowMultiple: false,
  },
  {
    SK: 4,
    systemRequired: false,
    defaultPosition: 5,
    code: 'GENDER',
    display: 'Gender',
    allowMultiple: false,
  },
  {
    SK: 5,
    systemRequired: false,
    defaultPosition: 6,
    code: 'EMAIL',
    display: 'Email',
    allowMultiple: false,
  },
  {
    SK: 6,
    systemRequired: false,
    defaultPosition: 7,
    code: 'PHONE',
    display: 'Phone',
    allowMultiple: false,
  },
  {
    SK: 7,
    systemRequired: false,
    defaultPosition: 8,
    code: 'PREFERRED_CONTACT_METHOD',
    display: 'Preferred Contact Method',
    allowMultiple: false,
  },

  {
    SK: 8,
    systemRequired: false,
    defaultPosition: 9,
    code: 'NOTIFICATIONS',
    display: 'Notifications',
    allowMultiple: false,
  },
  {
    SK: 9,
    systemRequired: false,
    defaultPosition: 10,
    code: 'ADDRESS',
    display: 'Address',
    allowMultiple: false,
  },
  {
    SK: 10,
    systemRequired: false,
    defaultPosition: 11,
    code: 'IDENTIFICATION',
    display: 'Identification',
    allowMultiple: false,
  },
];

const TestPage = () => {
  const [loading, setLoading] = useState(false);
  const [authTokens, setAuthTokens] = useState({ network: null, organization: null });
  const [locationsList, setLocationsList] = useState([]);
  const [servicesList, setServicesList] = useState([]);
  const [demographicFields, setDemographicFields] = useState({
    bookingWidgetFields: [...organizationAllFields],
    questionnaireWidgetFields: [...organizationAllFields],
    registrationWidgetFields: [...organizationAllFields],
    organizationRequiredFields: [],
  });
  const [allIdTypes, setAllIdTypes] = useState([]);
  const [bookingWidgets, setBookingWidgets] = useState([]);
  const [questionnaireWidgets, setQuestionnaireWidgets] = useState([]);
  const [registrationWidgets, setRegistrationWidgets] = useState([]);
  const [allWidgets, setAllWidgets] = useState([]);
  const [publicQuestionnaireList, setPublicQuestionnaireList] = useState([]);
  const [privateQuestionnaireList, setPrivateQuestionnaireList] = useState([]);
  const [questionnaireDefinition, setQuestionnaireDefinition] = useState();
  const [currentWidget, setCurrentWidget] = useState(null);

  const WIDGET_LAMBDA_BASE_URL = import.meta.env.VITE_BASE_URL;
  const VITE_WIDGET_BASE_URL = 'https://develop.d2yc83ebf3vw6y.amplifyapp.com/';
  // const orgId = '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53';
  const orgId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';

  const MACHINE_ACCESS_TOKEN = (env) => `${env}-machine-access-token`;
  const getAccessTokenFromCookie = async (env) => {
    const accessToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith(`${MACHINE_ACCESS_TOKEN(env)}=`))
      ?.split('=')[1];

    return accessToken;
  };
  const getMachineAccessToken = async (targetAwsEnv = 'PRIVATE') => {
    try {
      const env = targetAwsEnv === 'PUBLIC' ? 'network' : 'organization';
      if (env !== 'network' && env !== 'organization') {
        throw new Error('env should be network or organization');
      }
      if (typeof window === 'undefined') {
        throw new Error('Not running this code in browser env.');
      }
      let accessToken = await getAccessTokenFromCookie(env);

      // Refetch access token if it does not exist. It is either the first time or if it expired
      if (!accessToken) {
        const res = await fetch(`https://test.d2yc83ebf3vw6y.amplifyapp.com/api/auth/machineToken/${env}`, {
          cache: 'no-store',
        });
        if (!res.ok) {
          console.log('Fetching access token failed with status', res.status);
          throw res;
        }
        const data = await res.json();
        accessToken = data.accessToken;
      }
      setAuthTokens((prevTokens) => ({ ...prevTokens, [env]: accessToken }));
    } catch (e) {
      console.log('get machine access token failed');
      throw e;
    }
  };

  useEffect(() => {
    getMachineAccessToken('PUBLIC');
    getMachineAccessToken('PRIVATE');
  }, []);

  const fetchLocationsList = async () => {
    let orgId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';
    const url = NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL + GET_LOCATIONS_LIST.replace(ORGANIZATION_ID, orgId);
    fetch(url, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.organization,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        let list = data?.entry?.map((location) => location.resource);
        setLocationsList(list);
      })
      .catch((error) => {
        console.log('error', error);
        // open error toast here
      });
  };

  const fetchServicesList = async () => {
    let orgId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';
    const url = NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL + GET_SERVICES_LIST.replace(ORGANIZATION_ID, orgId);
    fetch(url, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.organization,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        let list = data?.compose?.include[0]?.concept.map((option) => ({ ...option, name: option.display }));
        setServicesList(list);
      })
      .catch((error) => {
        console.log('error', error);
        // open error toast here
      });
  };

  const fetchRequiredDemographicFields = () => {
    const orgId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';
    const url = NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL + `/organizations/${orgId}`;

    fetch(url, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.organization,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        // const requiredFields = data?.clientInformation.filter((attr) => attr.allowed).map((attr) => attr.attribute);
        const idTypes = data?.idTypes || [];
        setDemographicFields((prevState) => ({
          ...prevState,
          organizationRequiredFields: data?.clientInformation,
          idTypes: idTypes,
        }));
      })
      .catch((err) => {
        console.log('questionnaire list error', err);
      });
  };

  const fetchAllIdTypes = async () => {
    const url = NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL + '/id-types';
    try {
      const response = await fetch(url, {
        headers: {
          Authorization: 'Bearer ' + authTokens?.organization,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch idTypes');
      }

      const data = await response.json();
      setAllIdTypes(data?.idTypes || []);
    } catch (error) {
      console.error('Error fetching all idTypes:', error);
    }
  };

  const fetchQuestionnairesList = async () => {
    const orgId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';
    const publicUrl =
      NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL +
      `/organizations/${orgId}/questionnaires?contentStatus=final&publishStatus=public,both`;

    const privateUrl =
      NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL +
      `/organizations/${orgId}/questionnaires?contentStatus=final&publishStatus=private`;

    fetch(publicUrl, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.network,
      },
    })
      .then((res) => res.json())
      .then((data) => setPublicQuestionnaireList(data))
      .catch((err) => {
        console.log('questionnaire list error', err);
        // open error toast here
      });

    fetch(privateUrl, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.organization,
      },
    })
      .then((res) => res.json())
      .then((data) => setPrivateQuestionnaireList(data))
      .catch((err) => console.log('private questionnaire list fetching error', err));
  };

  const fetchPublicQuestionnaire = (questionnaireId) => {
    console.log('fetch questionnarie from public repository, questionnaireId: ', questionnaireId);
    setLoading(true);

    const url =
      NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL + `/organizations/${orgId}/questionnaires/${questionnaireId}`;

    fetch(url, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.network,
      },
    })
      .then((res) => res.json())
      .then((data) => setQuestionnaireDefinition(data?.questionnaire))
      .catch((err) => {
        console.log('questionnaire definition error', err);
        // open error toast here
      })
      .finally(() => setLoading(false));
  };

  const fetchPrivateQuestionnaire = (questionnaireId) => {
    console.log('fetch questionnarie from private repository, questionnaireId: ', questionnaireId);
    setLoading(true);

    const url =
      NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL + `/organizations/${orgId}/questionnaires/${questionnaireId}`;

    fetch(url, {
      headers: {
        Authorization: 'Bearer ' + authTokens?.organization,
      },
    })
      .then((res) => res.json())
      .then((data) => setQuestionnaireDefinition(data?.questionnaire))
      .catch((err) => {
        console.log('questionnaire definition error', err);
        // open error toast here
      })
      .finally(() => setLoading(false));
  };

  const handleQuestionnaireDropdown = (repository, questionnaireId) => {
    if (repository === repositoryTypes.public) {
      fetchPublicQuestionnaire(questionnaireId);
    } else {
      fetchPrivateQuestionnaire(questionnaireId);
    }
  };

  const generateImageHash = (base64String) => {
    return MD5(base64String).toString();
  };
  const prepareMapPlaceholderImage = (widgetData) => {
    const updatedWidgetData = { ...widgetData };

    if (updatedWidgetData.mapPlaceholderImage?.base64 && !updatedWidgetData.enableMap) {
      if (!updatedWidgetData.mapPlaceholderImage.imageHash) {
        updatedWidgetData.mapPlaceholderImage.imageHash = generateImageHash(
          updatedWidgetData.mapPlaceholderImage.base64,
        );
      }
    } else if (updatedWidgetData.enableMap) {
      updatedWidgetData.mapPlaceholderImage = {
        filename: '',
        base64: '',
      };
    }

    return updatedWidgetData;
  };

  const fetchBookingWidgetsList = async () => {
    await fetch(WIDGET_LAMBDA_BASE_URL + GET_ALL_BOOKING_WIDGET.replace(ORGANIZATION_ID, orgId))
      .then((res) => res.json())
      .then((data) => setBookingWidgets(data?.bookingWidgets))
      .catch((err) => console.log('booking error', err));
  };

  const fetchQuestionnaireWidgetsList = async () => {
    await fetch(WIDGET_LAMBDA_BASE_URL + GET_ALL_QUESTIONNAIRE_WIDGET.replace(ORGANIZATION_ID, orgId))
      .then((res) => res.json())
      .then((data) => setQuestionnaireWidgets(data?.questionnaireWidgets))
      .catch((err) => console.log('questionnaire error', err))
      .finally(() => setLoading(false));
  };

  const fetchRegistrationWidgetsList = async () => {
    await fetch(WIDGET_LAMBDA_BASE_URL + GET_ALL_REGISTRATION_WIDGET.replace(ORGANIZATION_ID, orgId))
      .then((res) => res.json())
      .then((data) => setRegistrationWidgets(data?.registrationWidgets))
      .catch((err) => console.log('registration error', err));
  };

  const fetchAllWidgetsList = () => {
    fetchBookingWidgetsList();
    fetchQuestionnaireWidgetsList();
    fetchRegistrationWidgetsList();
  };

  useEffect(() => {
    // fetchDemographicFields();
    fetchAllWidgetsList();
  }, []);

  useEffect(() => {
    if (authTokens.network && authTokens.organization) {
      fetchLocationsList();
      fetchServicesList();
      fetchRequiredDemographicFields();
      fetchAllIdTypes();
      fetchQuestionnairesList();
    }
  }, [authTokens]);

  useEffect(() => {
    if (bookingWidgets || registrationWidgets || questionnaireWidgets) {
      const sortKey = 'name';
      const widgetsList = mergeAndSortWidgets(questionnaireWidgets, bookingWidgets, registrationWidgets, sortKey);
      setAllWidgets(widgetsList);
    }
  }, [bookingWidgets, questionnaireWidgets, registrationWidgets]);
  const headers = {
    'Content-Type': 'application/json',
  };

  const updateBookingWidget = async (widgetData, orgId) => {
    try {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);

      const response = await fetch(
        WIDGET_LAMBDA_BASE_URL +
          BOOKING_WIDGET_ENDPOINT.replace(ORGANIZATION_ID, orgId).replace(
            BOOKING_WIDGET_ID,
            extractGUID(widgetData?.SK),
          ),
        {
          method: 'PUT',
          headers: headers,
          body: JSON.stringify(preparedWidgetData),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'TRANSLATIONS_NOT_FOUND' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while updating booking widget');
        }
        return { success: false };
      }

      const data = await response.json();
      if (data?.SK && data?.PK) {
        fetchBookingWidgetsList();
        alert('Booking widget updated successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while updating booking widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      alert('Error occurred while updating booking widget');
      console.log('error', error);
      return { success: false, error: error };
    }
  };

  const createBookingWidget = async (widgetData, orgId) => {
    try {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);

      const response = await fetch(WIDGET_LAMBDA_BASE_URL + CREATE_BOOKING_WIDGET.replace(ORGANIZATION_ID, orgId), {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(preparedWidgetData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'CANNOT_CREATE_WIDGET' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while creating booking widget');
        }
        return { success: false };
      }

      const data = await response.json();
      if (data?.SK && data?.PK) {
        fetchBookingWidgetsList();
        alert('Booking widget created successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while creating booking widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while creating booking widget');
      return { success: false, error: error };
    }
  };

  const importBookingWidget = async (widgetData, orgId) => {
    try {
      const preparedWidgetData = prepareMapPlaceholderImage(widgetData);

      const response = await fetch(WIDGET_LAMBDA_BASE_URL + IMPORT_BOOKING_WIDGET.replace(ORGANIZATION_ID, orgId), {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(preparedWidgetData),
      });

      if (!response.ok) {
        alert('Error occurred while importing booking widget');
        return { success: false };
      }

      const data = await response.json();
      if (data?.SK && data?.PK) {
        fetchBookingWidgetsList();
        alert('Booking widget imported successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while importing booking widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while importing booking widget');
      return { success: false, error: error };
    }
  };

  const updateQuestionnaireWidget = async (widgetData, orgId) => {
    try {
      const response = await fetch(
        WIDGET_LAMBDA_BASE_URL +
          QUESTIONNAIRE_WIDGET_ENDPOINT.replace(ORGANIZATION_ID, orgId).replace(
            QUESTIONNAIRE_WIDGET_ID,
            extractGUID(widgetData?.SK),
          ),
        {
          method: 'PUT',
          headers: headers,
          body: JSON.stringify(widgetData),
        },
      );
      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'TRANSLATIONS_NOT_FOUND' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while updating questionnaire widget');
        }
        return { success: false };
      }

      const data = await response.json();

      if (data?.SK && data?.PK) {
        fetchQuestionnaireWidgetsList();
        alert('Questionnaire widget updated successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while updating questionnaire widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while updating questionnaire widget');
      return { success: false, error: error };
    }
  };

  const createQuestionnaireWidget = async (widgetData, orgId, isImport) => {
    try {
      if (isImport && widgetData.action?.metaData?.actionConditions?.length) {
        const processedActionConditions = widgetData.action.metaData.actionConditions.map((condition) => {
          return {
            ...condition,
            scoreDefinitionName: '',
          };
        });

        widgetData = {
          ...widgetData,
          action: {
            ...widgetData.action,
            metaData: {
              ...widgetData.action.metaData,
              actionConditions: processedActionConditions,
            },
          },
        };
      }

      const URL = isImport ? IMPORT_QUESTIONNAIRE_WIDGET : CREATE_QUESTIONNAIRE_WIDGET;
      const response = await fetch(WIDGET_LAMBDA_BASE_URL + URL.replace(ORGANIZATION_ID, orgId), {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(widgetData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'CANNOT_CREATE_WIDGET' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while creating questionnaire widget');
        }
        return { success: false };
      }

      const data = await response.json();

      if (data?.SK && data?.PK) {
        fetchQuestionnaireWidgetsList();
        alert('Questionnaire widget created successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while creating questionnaire widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while creating questionnaire widget');
      return { success: false, error: error };
    }
  };

  const updateRegistrationWidget = async (widgetData, orgId) => {
    try {
      const response = await fetch(
        WIDGET_LAMBDA_BASE_URL +
          GET_REGISTRATION_WIDGET.replace(ORGANIZATION_ID, orgId).replace(
            REGISTRATION_WIDGET_ID,
            extractGUID(widgetData?.SK),
          ),
        {
          method: 'PUT',
          headers: headers,
          body: JSON.stringify(widgetData),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'TRANSLATIONS_NOT_FOUND' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while updating registration widget');
        }
        return { success: false };
      }

      const data = await response.json();

      if (data?.SK && data?.PK) {
        fetchRegistrationWidgetsList();
        alert('Registration widget updated successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while updating registration widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while updating registration widget');
      return { success: false, error: error };
    }
  };

  const createRegistrationWidget = async (widgetData, orgId) => {
    try {
      const response = await fetch(
        WIDGET_LAMBDA_BASE_URL + CREATE_REGISTRATION_WIDGET.replace(ORGANIZATION_ID, orgId),
        {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(widgetData),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData?.error === 'CANNOT_CREATE_WIDGET' && errorData?.message) {
          alert(errorData.message);
        } else {
          alert('Error occurred while creating registration widget');
        }
        return { success: false };
      }

      const data = await response.json();

      if (data?.SK && data?.PK) {
        fetchRegistrationWidgetsList();
        alert('Registration widget created successfully');
        return { success: true, response: data };
      } else {
        alert('Error occurred while creating registration widget: Invalid response');
        return { success: false };
      }
    } catch (error) {
      console.log('error', error);
      alert('Error occurred while creating registration widget');
      return { success: false, error: error };
    }
  };

  const fetchDeleteWidget = async (widgetId, widgetType) => {
    const url =
      WIDGET_LAMBDA_BASE_URL +
      DELETE_WIDGET.replace(ORGANIZATION_ID, orgId).replace(WIDGET_TYPE, widgetType).replace(WIDGET_ID, widgetId);

    const config = {
      method: 'DELETE',
      headers: headers,
      // data: requestBody,
    };

    setLoading(true);
    fetch(url, config)
      .then((res) => res.json())
      .then((data) => {
        console.log(data);
        if (widgetType === 'bookingWidget') {
          fetchBookingWidgetsList();
        } else if (widgetType === 'questionnaireWidget') {
          fetchQuestionnaireWidgetsList();
        } else if (widgetType === 'registrationWidget') {
          fetchRegistrationWidgetsList();
        }
      })
      .catch((err) => console.log('widget delete error', err))
      .finally(() => setLoading(false));
  };

  const onSaveOrUpdateWidgetCallback = async (widgetData, widgetType) => {
    console.log('save/update clicked', widgetData, widgetType);
    if (widgetData.SK && widgetData.PK) {
      const processedData = {
        ...widgetData,
        SK: widgetData.SK,
        PK: widgetData.PK,
        createdAt: widgetData.createdAt || new Date().toISOString(),
      };

      let response;
      switch (widgetType) {
        case BOOKING_CAPS:
          response = await updateBookingWidget(processedData, orgId);
          break;
        case QUESTIONNAIRE_CAPS:
          response = await updateQuestionnaireWidget(processedData, orgId);
          break;
        case REGISTRATION_CAPS:
          response = await updateRegistrationWidget(processedData, orgId);
          break;
        default:
          console.log('Invalid widget type');
          return { success: false, error: 'Invalid widget type' };
      }
      setLoading(false);
      return response;
    }
    // Only create a new widget if we don't have SK and PK
    let response;
    switch (widgetType) {
      case BOOKING_CAPS:
        response = await createBookingWidget(widgetData, orgId);
        break;
      case QUESTIONNAIRE_CAPS:
        response = await createQuestionnaireWidget(widgetData, orgId, false);
        break;
      case REGISTRATION_CAPS:
        response = await createRegistrationWidget(widgetData, orgId);
        break;
      default:
        return { success: false, error: 'Invalid widget type' };
    }
    setLoading(false);
    return response;
  };

  const handleSaveOrUpdateWidgetCallback = async (widgetData, widgetType) => {
    setLoading(true);
    const response = await onSaveOrUpdateWidgetCallback(widgetData, widgetType);
    if (response.success && response.response?.PK && response.response?.SK) {
      const widget = {
        PK: response.response.PK,
        SK: response.response.SK,
        widgetType: widgetType,
        currentLanguage: widgetData.currentLanguage || 'en',
      };
      setCurrentWidget(widget);

      response.widgetDetails = {
        ...widgetData,
        PK: response.response.PK,
        SK: response.response.SK,
      };
    }

    setLoading(false);
    return response;
  };

  const handleImportWidgetCallback = async (widgetData, widgetType) => {
    console.log('import clicked');
    setLoading(true);

    let response;
    switch (widgetType) {
      case BOOKING_CAPS:
        response = await importBookingWidget(widgetData, orgId);
        break;

      case QUESTIONNAIRE_CAPS:
        response = await createQuestionnaireWidget(widgetData, orgId, true);
        break;

      case REGISTRATION_CAPS:
        response = await createRegistrationWidget(widgetData, orgId);
        break;

      default:
        console.log('Invalid widget type');
        response = { success: false, error: 'Invalid widget type' };
        break;
    }

    setLoading(false);
    return response;
  };
  const handleExportWidgetCallback = async (widgetId, widgetType) => {
    setLoading(true);
    try {
      let widgetData;

      switch (widgetType) {
        case BOOKING_CAPS:
          widgetData = await fetchBookingWidgetById(widgetId, orgId);
          break;
        case QUESTIONNAIRE_CAPS:
          widgetData = await fetchQuestionnaireWidgetById(widgetId, orgId);
          break;
        case REGISTRATION_CAPS:
          widgetData = await fetchRegistrationWidgetById(widgetId, orgId);
          break;
        default:
          console.log('Invalid widget type');
          setLoading(false);
          return { success: false, error: 'Invalid widget type' };
      }

      if (!widgetData) {
        console.log(`Failed to fetch ${widgetType} data`);
        setLoading(false);
        return { success: false, error: `Failed to fetch ${widgetType} data` };
      }
      if (widgetData.mapPlaceholderImage?.imagePresignedUrl) {
        try {
          const response = await fetch(widgetData.mapPlaceholderImage.imagePresignedUrl);
          const blob = await response.blob();
          const base64data = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => {
              const base64 = reader.result.split(',')[1];
              resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });

          widgetData.mapPlaceholderImage.base64 = base64data;
          delete widgetData.mapPlaceholderImage.imagePresignedUrl;
        } catch (error) {
          console.error('Error converting image to base64:', error);
        }
      }

      const exportData = { ...widgetData };
      delete exportData.type;
      delete exportData.PK;
      delete exportData.SK;

      const fileName = exportData.name;

      const widgetJson = JSON.stringify(exportData, null, 2);
      downloadFileInJsonFormat(widgetJson, fileName);

      setLoading(false);
      return { success: true };
    } catch (error) {
      console.log('Error exporting widget', error);
      setLoading(false);
      return { success: false, error: error.message };
    }
  };

  const handlePreviewWidgetCallback = async (widgetId, widgetType) => {
    console.log('preview clicked');
    const base_url = VITE_WIDGET_BASE_URL;

    // Get the full widget data to access language information
    let widgetData;
    switch (widgetType) {
      case BOOKING_CAPS:
        widgetData = await fetchBookingWidgetById(widgetId, orgId);
        break;
      case QUESTIONNAIRE_CAPS:
        widgetData = await fetchQuestionnaireWidgetById(widgetId, orgId);
        break;
      case REGISTRATION_CAPS:
        widgetData = await fetchRegistrationWidgetById(widgetId, orgId);
        break;
      default:
        widgetData = null;
    }

    if (!widgetData) {
      console.error('Could not fetch widget data for preview');
      return;
    }

    let dynamicWidget = widgetData.dynamicWidget || false;
    const widgetURL = getWidgetURL(base_url, orgId, widgetId, widgetType, dynamicWidget);

    window.open(widgetURL);
  };

  const handleDuplicateWidgetCallback = async (widgetData, widgetType) => {
    console.log('duplicate clicked');
    let widgetId = extractGUID(widgetData?.SK);
    let widgetDetailedData;

    setLoading(true);
    let response;
    let updatedWidgetDetailedData;
    switch (widgetType) {
      case BOOKING_CAPS:
        widgetDetailedData = await fetchBookingWidgetById(widgetId, orgId);
        if (widgetDetailedData.mapPlaceholderImage?.imagePresignedUrl) {
          try {
            const response = await fetch(widgetDetailedData.mapPlaceholderImage.imagePresignedUrl);
            const blob = await response.blob();

            const base64data = await new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
              };
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            });

            widgetDetailedData.mapPlaceholderImage.base64 = base64data;
            delete widgetDetailedData.mapPlaceholderImage.imagePresignedUrl;
          } catch (error) {
            console.error('Error converting image to base64 during duplication:', error);
          }
        }

        updatedWidgetDetailedData = { ...widgetDetailedData };
        delete updatedWidgetDetailedData.PK;
        delete updatedWidgetDetailedData.SK;
        response = await createBookingWidget(updatedWidgetDetailedData, orgId);
        break;

      case QUESTIONNAIRE_CAPS:
        widgetDetailedData = await fetchQuestionnaireWidgetById(widgetId, orgId);
        updatedWidgetDetailedData = { ...widgetDetailedData };
        delete updatedWidgetDetailedData.PK;
        delete updatedWidgetDetailedData.SK;
        response = await createQuestionnaireWidget(updatedWidgetDetailedData, orgId, false);
        break;

      case REGISTRATION_CAPS:
        widgetDetailedData = await fetchRegistrationWidgetById(widgetId, orgId);
        updatedWidgetDetailedData = { ...widgetDetailedData };
        delete updatedWidgetDetailedData.PK;
        delete updatedWidgetDetailedData.SK;
        response = await createRegistrationWidget(updatedWidgetDetailedData, orgId);
        break;

      default:
        console.log('Invalid widget type');
        response = { success: false, error: 'Invalid widget type' };
        break;
    }

    setLoading(false);
    return response;
  };

  const handleDeleteWidgetCallback = async (widgetData, widgetType) => {
    console.log('delete clicked');
    const widgetId = extractGUID(widgetData?.SK);

    switch (widgetType) {
      case BOOKING_CAPS:
        return await fetchDeleteWidget(widgetId, 'bookingWidget');

      case QUESTIONNAIRE_CAPS:
        return await fetchDeleteWidget(widgetId, 'questionnaireWidget');

      case REGISTRATION_CAPS:
        return await fetchDeleteWidget(widgetId, 'registrationWidget');

      default:
        console.log('Invalid widget type');
        return { success: false, error: 'Invalid widget type' };
    }
  };

  const fetchBookingWidgetById = async (widgetId, orgId, language) => {
    console.log('IDS', widgetId, orgId);
    try {
      const url =
        WIDGET_LAMBDA_BASE_URL +
        BOOKING_WIDGET_ENDPOINT.replace(ORGANIZATION_ID, orgId).replace(BOOKING_WIDGET_ID, widgetId);

      // Add language parameter if provided
      const finalUrl = language ? `${url}?lang=${language}` : url;

      const response = await fetch(finalUrl);
      if (!response.ok) {
        console.log('Failed to fetch booking widget details');
        return null;
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.log('Error fetching booking widget details', error);
      return null;
    }
  };

  const fetchQuestionnaireWidgetById = async (widgetId, orgId, language) => {
    console.log('IDS', widgetId, orgId);

    try {
      const url =
        WIDGET_LAMBDA_BASE_URL +
        QUESTIONNAIRE_WIDGET_ENDPOINT.replace(ORGANIZATION_ID, orgId).replace(QUESTIONNAIRE_WIDGET_ID, widgetId);

      // Add language parameter if provided
      const finalUrl = language ? `${url}?lang=${language}` : url;

      const response = await fetch(finalUrl);
      if (!response.ok) {
        console.log('Failed to fetch questionnaire widget details');
        return null;
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.log('Error fetching questionnaire widget details', error);
      return null;
    }
  };

  const fetchRegistrationWidgetById = async (widgetId, orgId, language) => {
    console.log('IDS', widgetId, orgId);

    try {
      const url =
        WIDGET_LAMBDA_BASE_URL +
        GET_REGISTRATION_WIDGET.replace(ORGANIZATION_ID, orgId).replace(REGISTRATION_WIDGET_ID, widgetId);

      // Add language parameter if provided
      const finalUrl = language ? `${url}?lang=${language}` : url;

      const response = await fetch(finalUrl);
      if (!response.ok) {
        console.log('Failed to fetch registration widget details');
        return null;
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.log('Error fetching registration widget details', error);
      return null;
    }
  };

  useEffect(() => {
    setDemographicFields((prevState) => {
      const updateFields = (fields) =>
        fields.map((field) => {
          const requiredField = prevState.organizationRequiredFields?.find(
            (orgField) => orgField.attribute === field.code,
          );
          const allowed = requiredField ? requiredField.allowed : false;
          const mandatory = requiredField ? requiredField.required : false;
          const match = requiredField ? requiredField.match : false;
          const multiple = requiredField && requiredField?.multiple ? requiredField?.multiple : false;

          if (field.code === 'IDENTIFICATION') {
            return {
              ...field,
              systemRequired: allowed,
              systemMandatory: mandatory,
              isMandatory: mandatory,
              idTypes: demographicFields.idTypes || [],
              systemAllowMultiple: multiple,
              match,
            };
          }
          return {
            ...field,
            systemRequired: allowed,
            systemMandatory: mandatory,
            isMandatory: mandatory,
            systemAllowMultiple: multiple,
            match,
          };
        });

      return {
        ...prevState,
        bookingWidgetFields: updateFields(prevState.bookingWidgetFields),
        questionnaireWidgetFields: updateFields(prevState.questionnaireWidgetFields),
        registrationWidgetFields: updateFields(prevState.registrationWidgetFields),
      };
    });
  }, [organizationAllFields, demographicFields.organizationRequiredFields, demographicFields.idTypes]);

  return (
    <Box sx={{ px: '4%', py: '24px' }}>
      <Loader active={loading} />
      <WidgetEditor
        // common props
        widgetsList={allWidgets}
        organizationId={orgId}
        widgetBaseUrl={VITE_WIDGET_BASE_URL}
        handleImportWidgetCallback={handleImportWidgetCallback}
        handlePreviewWidgetCallback={handlePreviewWidgetCallback}
        handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
        handleDeleteWidgetCallback={handleDeleteWidgetCallback}
        handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWidgetCallback}
        allIdTypes={allIdTypes}
        orgRequiredIdTypes={demographicFields?.idTypes}
        fetchBookingWidgetById={fetchBookingWidgetById}
        fetchQuestionnaireWidgetById={fetchQuestionnaireWidgetById}
        fetchRegistrationWidgetById={fetchRegistrationWidgetById}
        handleExportWidgetCallback={handleExportWidgetCallback}
        // booking props
        bookingWidgetFields={demographicFields?.bookingWidgetFields}
        servicesList={servicesList}
        locationsList={locationsList}
        // questionnaire props
        questionnaireWidgetFields={demographicFields?.questionnaireWidgetFields}
        publicQuestionnaireList={publicQuestionnaireList}
        privateQuestionnaireList={privateQuestionnaireList}
        handleQuestionnaireDropdownCallback={handleQuestionnaireDropdown}
        questionnaireDefinition={questionnaireDefinition}
        setQuestionnaireDefinition={setQuestionnaireDefinition}
        // registration props
        registrationWidgetFields={demographicFields?.registrationWidgetFields}
        currentWidget={currentWidget}
      />
    </Box>
  );
};

export default TestPage;
