import React, { useState } from 'react';
import { Paper, Grid, Typography, Switch, TextField } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import { Workflow } from '@/components/Workflow';
import strings from '@/utils/localization';

export const BookingWorkflow = (props) => {
  const { validationData, existingWidgetData, widgetsList } = props;
  const { errors, control, setValue, getValues } = validationData;

  const { confirmationPage } = existingWidgetData || {};
  const formConfirmationValue = getValues('isConfirmationChecked');
  const [isConfirmation, setIsConfirmation] = useState(formConfirmationValue ?? confirmationPage?.enabled ?? true);

  return (
    <>
      <Workflow existingWidgetData={existingWidgetData} validationData={validationData} widgetsList={widgetsList} />

      <Grid container alignItems="center" mb={2}>
        <Grid item xs={4} lg={3}>
          <Typography mt={2}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.confirmationTooltip}</Typography>}
            >
              <span>{strings.confirmation}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isConfirmationChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  id="confirmation-toggle"
                  size="small"
                  checked={isConfirmation}
                  onChange={(event, val) => {
                    setIsConfirmation(val);
                    field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>
      {isConfirmation && (
        <Paper sx={{ my: 1, border: '1px solid #F0F0F0', borderRadius: '5px 5px 0 0', px: { xs: 2, sm: 1 }, py: 2 }}>
          <Grid container rowGap={2} sx={{ my: 2 }}>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <Typography>
                  <span>{strings.heading}</span>
                </Typography>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  control={control}
                  name="confirmationHeading"
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      size="small"
                      autoComplete="off"
                      type="text"
                      name="confirmationHeading"
                      placeholder={strings.confirmationPageHeadingPlaceholder}
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      error={!!errors?.confirmationHeading}
                      helperText={errors?.confirmationHeading?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <Typography>
                  <span>{strings.description}</span>
                </Typography>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  name="confirmationDescription"
                  control={control}
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      size="small"
                      fullWidth
                      autoComplete="off"
                      multiline
                      minRows={6}
                      name="confirmationDescription"
                      placeholder={strings.confirmationPageDescriptionPlaceholder}
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      error={!!errors?.confirmationDescription}
                      helperText={errors?.confirmationDescription?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <span>{strings.button}</span>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  control={control}
                  name={'confirmationButton'}
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      size="small"
                      placeholder={strings.next}
                      type="text"
                      name={'confirmationButton'}
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      autoComplete="off"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>
        </Paper>
      )}
    </>
  );
};
