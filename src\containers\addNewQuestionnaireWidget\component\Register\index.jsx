import React from 'react';
import { Box, Grid, Paper, Stack, TextField, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import strings from '@/utils/localization';

export const Register = (props) => {
  const { validationData } = props;
  const { register, errors } = validationData;

  return (
    <Box>
      <Typography>
        <CambianTooltip
          placement="right"
          title={<Typography variant="caption">{strings.registerPageTooltip}</Typography>}
        >
          <span>{strings.register} Page</span>
        </CambianTooltip>
      </Typography>
      <Paper
        sx={{
          my: 1,
          border: '1px solid #F0F0F0',
          borderRadius: '5px 5px 0 0',
          px: { xs: 2, sm: 3 },
          py: 2,
        }}
      >
        <Stack spacing={2}>
          <Grid container alignItems="center">
            <Grid item xs={4} lg={3}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.headingTooltip}</Typography>}
              >
                <span>{strings.heading}</span>
              </CambianTooltip>
            </Grid>
            <Grid item xs={8} lg={9}>
              <TextField
                sx={{ maxWidth: '100%' }}
                fullWidth
                size="small"
                variant="outlined"
                name="registerPageHeading"
                id="registerPageHeading"
                {...register('registerPageHeading')}
                error={!!errors?.registerPageHeading}
                helperText={errors?.registerPageHeading?.message}
                placeholder={strings.headingPlaceholder}
              />
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={4} lg={3}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.registerButtonTextTooltip}</Typography>}
              >
                <span>Register Button Text</span>
              </CambianTooltip>
            </Grid>
            <Grid item xs={8} lg={9}>
              <TextField
                sx={{ maxWidth: '100%' }}
                fullWidth
                size="small"
                variant="outlined"
                name="registerButtonText"
                id="registerButtonText"
                {...register('registerButtonText')}
                error={!!errors?.registerButtonText}
                helperText={errors?.registerButtonText?.message}
                placeholder="Register and Connect"
              />
            </Grid>
          </Grid>
        </Stack>
      </Paper>
    </Box>
  );
};
