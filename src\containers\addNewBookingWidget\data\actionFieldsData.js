import strings from '@/utils/localization';

export const actionFieldsData = {
  actionDropDown: [
    {
      id: 0,
      label: strings.actionPage,
      value: 'Page',
    },
    {
      id: 1,
      label: strings.actionURL,
      value: 'URL',
    },
    {
      id: 2,
      label: strings.actionWidget,
      value: 'Widget',
    },
    {
      id: 3,
      label: strings.actionService,
      value: 'Service',
    },
  ],
  widgetTypes: [
    {
      id: 0,
      label: strings.bookingWidgetType,
      value: 'Booking Widget',
    },
    {
      id: 1,
      label: strings.questionnaireWidgetType,
      value: 'Questionnaire Widget',
    },
    {
      id: 2,
      label: strings.registrationWidgetType,
      value: 'Registration Widget',
    },
  ],
  targetDropDown: [
    {
      id: 0,
      label: strings.targetNewTab,
      value: 'Open in new Tab',
    },
    {
      id: 1,
      label: strings.targetSameTab,
      value: 'Open in same Tab',
    },
    {
      id: 2,
      label: strings.targetNewWindow,
      value: 'Open in new Window',
    },
    {
      id: 3,
      label: strings.targetSameIFrame,
      value: 'Open in same IFrame',
    },
  ],
  selectedTarget: '',
  selectedServices: [],
  selectedWidgetType: '',
  selectedWidget: { name: '', id: null },
  serviceDropdown: [
    {
      id: 0,
      label: strings.serviceCallBackgroundURL,
      value: 'Call Background URL',
    },
  ],
  selectedAction: '',
  selectedService: '',
  backgroundServiceEndpoint: '',
  redirectUrl: '',
  addDemographic: true,
  showDemographic: true,
  default: true,
};
