/* eslint-disable react/prop-types */
import React from 'react';
import { <PERSON>, <PERSON>er, GoogleApiWrapper } from 'google-maps-react';

export class GoogleMap extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <Map
        google={this.props.google}
        style={{ width: '90%', height: '90%' }}
        initialCenter={{
          lat: this.props.address.position.latitude,
          lng: this.props.address.position.longitude,
        }}
        zoom={12}
        onClick={this.props.onMapClicked}
      >
        <Marker
          title={this.props.address.name}
          position={{
            lat: this.props.address.position.latitude,
            lng: this.props.address.position.longitude,
          }}
        />
      </Map>
    );
  }
}

export default GoogleApiWrapper({
  apiKey: 'import.meta.env.VITE_GOOGLE_MAPS_API_KEY', // sending wrong KEY, not being used in v2
})(GoogleMap);
