import React, { useMemo, useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  TextField,
  Select,
  MenuItem,
  Switch,
  Button,
  FormHelperText,
  Stack,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import CloseIcon from '@mui/icons-material/Close';
import { RedirectToUrl } from './RedirectToUrl';
import { HeadingAndDescriptionAction } from './HeadingAndDescriptionAction';
import { CallService } from './CallService';
import { RedirectToWidget } from './RedirectToWidget';
import { actionFieldsData } from '../../data/actionFieldsData';
import { Add } from '@mui/icons-material';
import strings from '@/utils/localization';
import { Controller } from 'react-hook-form';
import { SCORE_DEFINITIONS_URL, SCORE_NAME_URL, MAX_CONDITION_LIMIT } from '@/utils/constants';

export const Actions = (props) => {
  const {
    isDemographicsEnabled,
    validationData,
    existingWidgetData,
    questionnaireDefinition,
    allAvailableWidgetsList,
    isDynamicWidget,
  } = props;

  const { errors, control, actionData, addField, removeField, setValue, getValues } = validationData;

  const { action } = existingWidgetData || {};

  const formActionEnabled = getValues('isActionChecked');
  const [isActionEnabled, setIsActionEnabled] = useState(formActionEnabled ?? action?.enabled ?? false);
  const [actionFields, setActionFields] = useState([...actionData]);

  useEffect(() => {
    const formValue = getValues('isActionChecked');
    setIsActionEnabled(formValue !== undefined ? formValue : action?.enabled ?? false);
    const savedActionData = getValues('actionData');
    if (savedActionData?.length) {
      setActionFields(savedActionData);
    }
  }, []);

  useMemo(() => {
    const savedActionData = getValues('actionData');
    if (existingWidgetData) {
      setIsActionEnabled(action?.enabled || false);
      setActionFields(action?.enabled ? action?.metaData?.actionConditions : savedActionData || [...actionData]);
    } else if (savedActionData?.length) {
      setActionFields(savedActionData);
    }
  }, [existingWidgetData]);

  const scoreList = useMemo(() => {
    if (questionnaireDefinition) {
      let scoreNames = [];
      for (let i = 0; i < questionnaireDefinition?.extension?.length; i++) {
        if (SCORE_DEFINITIONS_URL.includes(questionnaireDefinition?.extension[i]?.url)) {
          let scoreName = questionnaireDefinition?.extension[i]?.extension;
          for (let j = 0; j < scoreName.length; j++) {
            if (SCORE_NAME_URL.includes(scoreName[j]?.url)) {
              scoreNames.push({
                name: scoreName[j]?.valueString,
              });
            }
          }
        }
      }
      return scoreNames;
    }
    return [];
  }, [questionnaireDefinition]);

  const handleScoreName = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        scoreDefinitionName: event.target.value,
      };
      return updatedData;
    });
  };

  const handleScoreRange = (newValue, index) => {
    setValue(`actionData[${index}].selectedDropdownScore`, newValue?.props?.value?.label || '');
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedScore: newValue?.props?.value?.value,
        selectedDropdownScore: newValue?.props?.value?.label,
      };
      return updatedData;
    });
  };

  const handleScore = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        scoreValue: event.target.value,
      };
      return updatedData;
    });
  };

  const handleScoreFrom = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        scoreFrom: event.target.value,
      };
      return updatedData;
    });
  };
  const handleScoreTo = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        scoreTo: event.target.value,
      };
      return updatedData;
    });
  };

  const handleAction = (e, newValue, index) => {
    const selectedAction = e.target.value;
    const formActionData = getValues('actionData');
    let updatedActionData = [...formActionData];
    updatedActionData[index] = {
      ...updatedActionData[index],
      selectedAction: selectedAction,
    };
    if (selectedAction === 'Widget') {
      updatedActionData[index].selectedWidgetType = '';
      updatedActionData[index].selectedWidget = '';
    }
    setValue('actionData', updatedActionData);

    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedAction: selectedAction,
      };
      if (selectedAction === 'Widget') {
        updatedData[index].selectedWidgetType = '';
        updatedData[index].selectedWidget = '';
      }
      return updatedData;
    });
  };

  const handleTargetDropdown = (item, { target: { value } }, index) => {
    const selectedTarget = value.value;
    const formActionData = getValues('actionData');
    let updatedActionData = [...formActionData];
    updatedActionData[index] = {
      ...updatedActionData[index],
      selectedTarget: selectedTarget,
    };
    setValue('actionData', updatedActionData);

    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        selectedTarget: selectedTarget,
      };
      return updatedData;
    });
  };

  const handleAddActionCondition = () => {
    addField();
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData, { ...actionFieldsData, default: false }];
      return updatedData;
    });
  };

  const handleRemoveActionCondition = (index) => {
    removeField(index);
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData.splice(index, 1);
      return updatedData;
    });
  };

  return (
    <Box sx={{ flexGrow: 5 }}>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.actionsTooltip}</Typography>}
            >
              <span>
                {strings.actions}
                {isActionEnabled && ' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isActionChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  name="isActionChecked"
                  checked={isActionEnabled}
                  onChange={(event, val) => {
                    setIsActionEnabled(val);
                    field?.onChange(val);
                  }}
                  id="actionToggle"
                />
              );
            }}
          />
        </Grid>
      </Grid>

      {/* Button Name Field */}

      {isActionEnabled && (
        <Grid container sx={{ mt: 2 }} alignItems="center">
          <Grid item xs={4} lg={3}>
            <Typography>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.actionButtonNameTooltip}</Typography>}
              >
                <span>{strings.button}</span>
              </CambianTooltip>
            </Typography>
          </Grid>

          <Grid item xs={7} lg={9}>
            <Controller
              name="actionButtonText"
              control={control}
              render={({ field: { ref, ...field } }) => (
                <TextField
                  {...field}
                  fullWidth
                  size="small"
                  name="actionButtonText"
                  type="text"
                  onChange={(event) => {
                    if (event.target.value.trim()) {
                      field.onChange(event.target.value);
                    } else {
                      field.onChange(event.target.value.trim());
                    }
                  }}
                  placeholder={strings.next}
                  error={!!errors?.buttonName}
                  helperText={errors?.buttonName?.message}
                />
              )}
            />
          </Grid>
        </Grid>
      )}

      {isActionEnabled &&
        actionData &&
        actionData.length &&
        actionData?.map((item, index) => (
          <Paper key={item.id} sx={{ my: 1, pb: 2, border: '1px solid #F0F0F0', borderRadius: '5px 5px 0 0' }}>
            <Stack
              direction="row"
              justifyContent="space-between"
              sx={{ backgroundColor: 'background.secondary', p: 1 }}
            >
              <Typography variant="body1">{item.default ? strings.default : `Condition ${index}`}</Typography>
              {!item.default && actionFields.length > 1 && (
                <CloseIcon
                  onClick={() => handleRemoveActionCondition(index)}
                  sx={{
                    cursor: 'pointer',
                    fontSize: '20px',
                  }}
                />
              )}
            </Stack>
            <Stack sx={{ p: 1, mt: 1 }} rowGap={2}>
              {!item.default && (
                <Grid>
                  <Grid container alignItems="center">
                    <Grid item xs={4} lg={3}>
                      <Typography>
                        <CambianTooltip
                          placement="right"
                          title={<Typography variant="caption">{strings.scoreNameTooltip}</Typography>}
                        >
                          <span>
                            {strings.scoreName}
                            {' *'}
                          </span>
                        </CambianTooltip>
                      </Typography>
                    </Grid>
                    <Grid item xs={8} lg={9}>
                      <Controller
                        name={`actionData[${index}].scoreDefinitionName`}
                        control={control}
                        render={({ field }) => (
                          <Select
                            size="small"
                            fullWidth
                            sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                            name={`actionData[${index}].scoreDefinitionName`}
                            value={field?.value !== undefined ? field?.value : ''}
                            displayEmpty
                            onChange={(event, newValue) => {
                              handleScoreName(event, index);
                              return field?.onChange(newValue?.props?.value);
                            }}
                            renderValue={(selected) => {
                              if (typeof selected === 'undefined' || selected.length === 0) {
                                return (
                                  <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>
                                    {strings.scoreNamePlaceholder}
                                  </Typography>
                                );
                              }
                              return selected;
                            }}
                            error={
                              errors?.actionData !== undefined
                                ? !!errors?.actionData[index]?.scoreDefinitionName
                                : false
                            }
                          >
                            {scoreList?.length ? (
                              scoreList?.map((score, index) => (
                                <MenuItem value={score.name} key={index}>
                                  {score.name}
                                </MenuItem>
                              ))
                            ) : (
                              <MenuItem>
                                {questionnaireDefinition && scoreList?.length === 0
                                  ? strings.noScoresAvailable
                                  : strings.selectQuestionnaireFirst}
                              </MenuItem>
                            )}
                          </Select>
                        )}
                      />
                      {errors?.actionData !== undefined ? (
                        <FormHelperText error sx={{ pl: 1 }}>
                          {errors?.actionData[index]?.scoreDefinitionName?.message}
                        </FormHelperText>
                      ) : (
                        ''
                      )}
                    </Grid>
                  </Grid>
                  <Grid container alignItems="center" pt={2}>
                    <Grid item xs={4} lg={3}>
                      <Typography>
                        <CambianTooltip
                          placement="right"
                          title={<Typography variant="caption">{strings.operatorTooltip}</Typography>}
                        >
                          <span>
                            {strings.operator}
                            {' *'}
                          </span>
                        </CambianTooltip>
                      </Typography>
                    </Grid>
                    <Grid item xs={8} lg={9}>
                      <Controller
                        name={`actionData[${index}].selectedScore`}
                        control={control}
                        render={({ field }) => (
                          <Select
                            size="small"
                            fullWidth
                            sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                            name={`actionData[${index}].selectedScore`}
                            displayEmpty
                            value={actionFields[index]?.selectedDropdownScore}
                            onChange={(event, newValue) => {
                              handleScoreRange(newValue, index);
                              return field.onChange(newValue?.props?.value?.value);
                            }}
                            renderValue={(selected) => {
                              if (typeof selected === 'undefined' || selected.length === 0) {
                                return (
                                  <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>
                                    {strings.operatorPlaceholder}
                                  </Typography>
                                );
                              }
                              return selected;
                            }}
                            error={
                              errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedScore : false
                            }
                          >
                            {item?.scoreDropDown?.map((score, index) => (
                              <MenuItem value={score} key={index}>
                                {score.label}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      <FormHelperText error sx={{ pl: 1 }}>
                        {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedScore?.message : ''}
                      </FormHelperText>
                    </Grid>
                  </Grid>
                  {actionFields[index]?.selectedDropdownScore &&
                    actionFields[index]?.selectedDropdownScore !== 'Between' && (
                      <Grid container alignItems="center" pt={2}>
                        <Grid item xs={4} lg={3}>
                          <Typography>
                            <CambianTooltip
                              placement="right"
                              title={<Typography variant="caption">{strings.valueTooltip}</Typography>}
                            >
                              <span>
                                {strings.value}
                                {' *'}
                              </span>
                            </CambianTooltip>
                          </Typography>
                        </Grid>
                        <Grid item xs={8} lg={9}>
                          <Controller
                            name={`actionData[${index}].scoreValue`}
                            control={control}
                            render={({ field }) => (
                              <TextField
                                fullWidth
                                type="text"
                                size="small"
                                value={field?.value}
                                name={`actionData[${index}].scoreValue`}
                                placeholder={strings.valuePlaceholder}
                                onChange={(event) => {
                                  handleScore(event, index);
                                  return field?.onChange(event.target.value);
                                }}
                                error={
                                  errors?.actionData !== undefined ? !!errors?.actionData[index]?.scoreValue : false
                                }
                                helperText={
                                  errors?.actionData !== undefined ? errors?.actionData[index]?.scoreValue?.message : ''
                                }
                              />
                            )}
                          />
                        </Grid>
                      </Grid>
                    )}
                  {actionFields[index]?.selectedDropdownScore === 'Between' && (
                    <Grid container alignItems="center" rowGap={2} pt={2}>
                      <Grid item xs={4} lg={3}>
                        <Typography>
                          <CambianTooltip
                            placement="right"
                            title={<Typography variant="caption">{strings.valueMinTooltip}</Typography>}
                          >
                            <span>
                              {strings.valueMin}
                              {' *'}
                            </span>
                          </CambianTooltip>
                        </Typography>
                      </Grid>
                      <Grid item xs={8} lg={9}>
                        <Controller
                          name={`actionData[${index}].scoreFrom`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              fullWidth
                              type="text"
                              size="small"
                              value={field?.value}
                              name={`actionData[${index}].scoreFrom`}
                              placeholder={strings.valueMinPlaceholder}
                              onChange={(event) => {
                                handleScoreFrom(event, index);
                                return field?.onChange(event.target.value);
                              }}
                              error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.scoreFrom : false}
                              helperText={
                                errors?.actionData !== undefined ? errors?.actionData[index]?.scoreFrom?.message : ''
                              }
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={4} lg={3}>
                        <Typography>
                          <CambianTooltip
                            placement="right"
                            title={<Typography variant="caption">{strings.valueMaxTooltip}</Typography>}
                          >
                            <span>
                              {strings.valueMax}
                              {' *'}
                            </span>
                          </CambianTooltip>
                        </Typography>
                      </Grid>
                      <Grid item xs={8} lg={9}>
                        <Controller
                          name={`actionData[${index}].scoreTo`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              fullWidth
                              type="text"
                              size="small"
                              value={field?.value}
                              name={`actionData[${index}].scoreTo`}
                              placeholder={strings.valueMaxPlaceholder}
                              onChange={(event) => {
                                handleScoreTo(event, index);
                                return field?.onChange(event.target.value);
                              }}
                              error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.scoreTo : false}
                              helperText={
                                errors?.actionData !== undefined ? errors?.actionData[index]?.scoreTo?.message : ''
                              }
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              )}
              <Grid container alignItems="center">
                <Grid item xs={4} lg={3}>
                  <Typography>
                    <CambianTooltip
                      placement="right"
                      title={
                        <Typography variant="caption">
                          {item.default ? strings.defaultActionTooltip : strings.selectedActionTooltip}
                        </Typography>
                      }
                    >
                      <span>
                        {strings.action}
                        {' *'}
                      </span>
                    </CambianTooltip>
                  </Typography>
                </Grid>
                <Grid item xs={8} lg={9}>
                  <Controller
                    name={`actionData[${index}].selectedAction`}
                    control={control}
                    render={({ field }) => (
                      <Select
                        size="small"
                        fullWidth
                        sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                        name="action"
                        displayEmpty
                        value={field?.value}
                        onChange={(event, newValue) => {
                          handleAction(event, newValue, index);
                          return field?.onChange(newValue?.props?.value);
                        }}
                        renderValue={(selected) => {
                          if (typeof selected === 'undefined' || selected.length === 0) {
                            return (
                              <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>{strings.actionPlaceholder}</Typography>
                            );
                          }
                          return selected;
                        }}
                        error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedAction : false}
                      >
                        {item?.actionDropDown?.map((action, index) => (
                          <MenuItem value={action?.value} key={index}>
                            {action.value}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                  <FormHelperText error sx={{ pl: 1 }}>
                    {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedAction?.message : ''}
                  </FormHelperText>
                </Grid>
              </Grid>
              {actionFields[index]?.selectedAction === 'Page' && (
                <HeadingAndDescriptionAction
                  validationData={validationData}
                  data={{ item, index }}
                  actionFieldsData={actionFieldsData}
                  enableButton={false}
                />
              )}
              {actionFields[index]?.selectedAction === 'URL' && (
                <RedirectToUrl
                  validationData={validationData}
                  data={{ item, index }}
                  actionFields={actionFields}
                  setActionFields={setActionFields}
                />
              )}
              {(actionFields[index]?.selectedAction === 'Widget' ||
                actionFields[index]?.selectedAction === 'Redirect to another widget in IFrame') && (
                <RedirectToWidget
                  validationData={validationData}
                  data={{ item, index }}
                  actionFields={actionFields}
                  setActionFields={setActionFields}
                  allAvailableWidgetsList={allAvailableWidgetsList}
                />
              )}
              {(actionFields[index]?.selectedAction === 'Service' ||
                actionFields[index]?.selectedAction === 'Call a service in background') && (
                <CallService
                  validationData={validationData}
                  data={{ item, index }}
                  actionFields={actionFields}
                  setActionFields={setActionFields}
                />
              )}

              {(actionFields[index]?.selectedAction === 'Widget' ||
                actionFields[index]?.selectedAction === 'Redirect to another widget in IFrame' ||
                actionFields[index]?.selectedAction === 'URL' ||
                actionFields[index]?.selectedAction === 'URL') && (
                <Stack rowGap={2}>
                  <Grid container alignItems="center">
                    <Grid item xs={4} lg={3}>
                      <Typography>
                        <span>
                          {strings.target}
                          {' *'}
                        </span>
                      </Typography>
                    </Grid>
                    <Grid item xs={8} lg={9}>
                      <Controller
                        name={`actionData[${index}].selectedTarget`}
                        control={control}
                        render={({ field: { ref, value, ...field } }) => (
                          <Select
                            id="selected-target"
                            size="small"
                            fullWidth
                            sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                            name={`actionData[${index}].selectedTarget`}
                            displayEmpty
                            value={value}
                            onChange={(event, newValue) => {
                              handleTargetDropdown(action, event, index);
                              field?.onChange(newValue?.props?.value?.label);
                            }}
                            renderValue={(selected) => {
                              if (typeof selected === 'undefined' || selected.length === 0) {
                                return (
                                  <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>
                                    {strings.targetPlaceholder}
                                  </Typography>
                                );
                              }
                              return selected;
                            }}
                            error={
                              errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedTarget : false
                            }
                          >
                            {item?.targetDropDown?.map((target) => (
                              <MenuItem value={target} key={target.id}>
                                {target.label}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      <FormHelperText error sx={{ pl: 1 }}>
                        {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedTarget?.message : ''}
                      </FormHelperText>
                    </Grid>
                  </Grid>
                </Stack>
              )}
            </Stack>
          </Paper>
        ))}

      {!isDynamicWidget && isActionEnabled && actionData.length < MAX_CONDITION_LIMIT && (
        <Grid item sm={12}>
          <Button
            onClick={handleAddActionCondition}
            startIcon={<Add sx={{ fontSize: '20px' }} />}
            color="primary"
            sx={{
              p: '15px 1px',
              cursor: 'pointer',
              fontSize: '15px',
              '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
            }}
          >
            {strings.addCondition}
          </Button>
        </Grid>
      )}
    </Box>
  );
};
