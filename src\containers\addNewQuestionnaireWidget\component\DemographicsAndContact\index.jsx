import React, { useEffect, useState } from 'react';
import {
  FormControlLabel,
  FormHelperText,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { ClientInformationFields } from '@/components/ClientInformationFields';
import { getAvailableFieldsList } from '@/utils/commonUtility';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { PATIENT, CLINICAL, IDENTIFIED, DEIDENTIFIED, UNIDENTIFIED } from '@/utils/constants';
import { CambianTooltip } from '@/components';

export const DemographicsAndContact = (props) => {
  const {
    isDemographicsEnabled,
    setIsDemographicsEnabled,
    validationData,
    fieldsValidationError,
    handleSelectFields,
    existingWidgetData,
    questionnaireWidgetFields,
    selectedIdTypes,
    setSelectedIdTypes,
    allIdTypes,
    orgRequiredIdTypes,
  } = props;
  const { control, setValue, getValues, reset, errors, register } = validationData;

  const { fields = [], provinces, identification: existingIdentification } = existingWidgetData || {};

  const [fieldsList, setFieldsList] = useState(getAvailableFieldsList(questionnaireWidgetFields, fields));

  useEffect(() => {
    setFieldsList(getAvailableFieldsList(questionnaireWidgetFields, fields));
  }, [questionnaireWidgetFields]);

  const [selectedJurisdictions, setSelectedJurisdictions] = useState(provinces || []);
  const [identification, setIdentification] = useState(existingIdentification || IDENTIFIED);

  const handleIdentificationChange = (event) => {
    const value = event.target.value;
    setIdentification(value);
    if (value === IDENTIFIED) {
      setIsDemographicsEnabled(true);
      setValue('isDemographicsEnabled', true);
    } else {
      setIsDemographicsEnabled(false);
      setValue('isDemographicsEnabled', false);
    }
  };

  return (
    <>
      {/* {identification === IDENTIFIED && ( */}
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.clientInformationTooltip}</Typography>}
            >
              <span>{strings.clientInformation}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
      </Grid>
      {/* )} */}

      <Grid container alignItems="center" sx={{ mt: 1, pr: { xs: 2, sm: 0 } }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.identificationTooltip}</Typography>}
            >
              <span>{strings.identification}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            name="identification"
            control={control}
            render={({ field: { value, ...field } }) => (
              <RadioGroup
                row
                name="identification"
                value={value}
                onChange={(e) => {
                  handleIdentificationChange(e);
                  field.onChange(e.target.value);
                }}
              >
                <FormControlLabel label={strings.identified} value={IDENTIFIED} control={<Radio />} />
                <FormControlLabel label={strings.deidentified} value={DEIDENTIFIED} control={<Radio />} />
                <FormControlLabel label={strings.unidentified} value={UNIDENTIFIED} control={<Radio />} />
              </RadioGroup>
            )}
          />
        </Grid>
      </Grid>

      {identification === IDENTIFIED && (
        <>
          <Grid container alignItems="center" sx={{ mt: 2 }}>
            <Grid item xs={4} lg={3}>
              <Typography>
                <CambianTooltip
                  placement="right"
                  title={<Typography variant="caption">{strings.clientInformationPageTitleTooltip}</Typography>}
                >
                  <span>{strings.title}</span>
                </CambianTooltip>
              </Typography>
            </Grid>
            <Grid item xs={8} lg={9}>
              <Controller
                control={control}
                name="clientInformationPageTitle"
                render={({ field: { ref, ...field } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    size="small"
                    autoComplete="off"
                    placeholder={strings.title}
                    type="text"
                    name="clientInformationPageTitle"
                    onChange={(event) => {
                      if (event.target.value.trim()) {
                        field.onChange(event.target.value);
                      } else {
                        field.onChange(event.target.value.trim());
                      }
                    }}
                    error={!!errors?.demographicPageHeading}
                    helperText={errors?.demographicPageHeading?.message}
                    sx={{ maxWidth: '100%' }}
                  />
                )}
              />
            </Grid>
          </Grid>

          <Grid container alignItems="center" sx={{ mt: 2 }}>
            <Grid item xs={4} lg={3}>
              <Typography>
                <CambianTooltip
                  placement="right"
                  title={<Typography variant="caption">{strings.clientInformationPageSubTitleTooltip}</Typography>}
                >
                  <span>{strings.subTitle}</span>
                </CambianTooltip>
              </Typography>
            </Grid>
            <Grid item xs={8} lg={9}>
              <Controller
                control={control}
                name="clientInformationPageSubtitle"
                render={({ field: { ref, ...field } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    size="small"
                    autoComplete="off"
                    placeholder={strings.subTitle}
                    type="text"
                    name="clientInformationPageSubtitle"
                    onChange={(event) => {
                      if (event.target.value.trim()) {
                        field.onChange(event.target.value);
                      } else {
                        field.onChange(event.target.value.trim());
                      }
                    }}
                    error={!!errors?.demographicPageHeading}
                    helperText={errors?.demographicPageHeading?.message}
                    sx={{ maxWidth: '100%' }}
                  />
                )}
              />
            </Grid>
          </Grid>

          <Paper
            sx={{
              mb: 2,
              mt: 1,
              p: 2,
              border: '1px solid #F0F0F0',
              borderRadius: '5px 5px 0 0',
              borderColor: fieldsValidationError ? '#F44336' : '#F0F0F0',
            }}
          >
            <ClientInformationFields
              fieldsList={fieldsList}
              setFieldsList={setFieldsList}
              handleSelectFields={handleSelectFields}
              validationData={validationData}
              selectedJurisdictions={selectedJurisdictions}
              setSelectedJurisdictions={setSelectedJurisdictions}
              selectedIdTypes={selectedIdTypes}
              setSelectedIdTypes={setSelectedIdTypes}
              allIdTypes={allIdTypes}
              orgRequiredIdTypes={orgRequiredIdTypes}
            />
          </Paper>
        </>
      )}

      {fieldsValidationError && <FormHelperText error>{fieldsValidationError}</FormHelperText>}
      {isDemographicsEnabled ? (
        <Grid container sx={{ mt: 2 }}>
          <Grid item xs={4} lg={3}>
            <Typography>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.verifyWithOTPTooltip}</Typography>}
              >
                <span>{strings.verifyWithOTP}</span>
              </CambianTooltip>
            </Typography>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              control={control}
              name="isOtpVerificationChecked"
              render={({ field: { ref, ...field } }) => {
                return (
                  <Switch
                    size="small"
                    name="isOtpVerificationChecked"
                    checked={field?.value !== undefined ? field?.value : false}
                    onChange={(event, val) => {
                      return field?.onChange(val);
                    }}
                    id="otpVerificationToggle"
                    disabled={isDemographicsEnabled === false}
                  />
                );
              }}
            />
          </Grid>
        </Grid>
      ) : (
        <></>
      )}
    </>
  );
};
