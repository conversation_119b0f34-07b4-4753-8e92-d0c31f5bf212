import React from 'react';
import { Box, Grid, Typography, TextField } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const RedirectToUrl = (props) => {
  const {
    data: { index },
    setActionFields,
    validationData,
  } = props;
  const { control, errors } = validationData;

  const handleActionUrl = (event, index) => {
    setActionFields((prevActionFieldsData) => {
      const updatedData = [...prevActionFieldsData];
      updatedData[index] = {
        ...updatedData[index],
        redirectUrl: event.target.value,
      };
      return updatedData;
    });
  };

  return (
    <Box>
      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.redirectUrlTooltip}</Typography>}
            >
              <span>
                {strings.redirectUrl}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            name={`actionData[${index}].redirectUrl`}
            control={control}
            render={({ field }) => (
              <TextField
                id="redirectUrl"
                fullWidth
                size="small"
                type="text"
                value={field?.value}
                name={`actionData[${index}].redirectUrl`}
                placeholder={strings.redirectUrlPlaceholder}
                onChange={(event) => {
                  handleActionUrl(event);
                  return field?.onChange(event.target.value);
                }}
                error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.redirectUrl : false}
                helperText={errors?.actionData !== undefined ? errors?.actionData[index]?.redirectUrl?.message : ''}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};
