import { ThemeProvider } from '@mui/material/styles';
import { CambianTheme } from './components';
import { CssBaseline, createTheme } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';
import TestPage from './TestPage';
import { NavBar } from './TestPage/NavBar';

export default function App() {
  const extendedTheme = createTheme(CambianTheme, {
    typography: {
      fontFamily: '"__Inter_d65c78", "Inter", "Helvetica", "Arial", sans-serif',
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            fontFamily: '"__Inter_d65c78", "Inter", "Helvetica", "Arial", sans-serif',
          },
        },
      },
    },
  });

  const snackbarIconVariants = {
    success: <TaskAlt fontSize="small" />,
    error: <ErrorOutline fontSize="small" />,
    info: <InfoOutlined fontSize="small" />,
    warning: <WarningAmberOutlined fontSize="small" />,
  };

  return (
    <ThemeProvider theme={extendedTheme}>
      <CssBaseline />
      <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
        <NavBar />
        <TestPage />
      </SnackbarProvider>
    </ThemeProvider>
  );
}
