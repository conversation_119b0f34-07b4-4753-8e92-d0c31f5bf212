import React, { useMemo, useState } from 'react';
import { MultiSelect } from './MultiSelect';
import { sortLists } from '@/utils/commonUtility';
import strings from '@/utils/localization';

export const Services = (props) => {
  const { validationData, handleSelectServices, availableServices, existingWidgetData } = props;

  const { errors, setValue, getValues, trigger } = validationData;
  const { services } = existingWidgetData || {};
  const availableServicesList = availableServices?.sort(sortLists);

  const [selectedOptions, setSelectedOptions] = useState(() => {
    const formServices = getValues('services');
    return formServices?.length ? formServices : services || [];
  });

  useMemo(() => {
    const formServices = getValues('services');
    if (formServices?.length) {
      setSelectedOptions(formServices);
    } else if (existingWidgetData?.services?.length) {
      setSelectedOptions(existingWidgetData.services);
      setValue('services', existingWidgetData.services);
    }
  }, [existingWidgetData]);

  const updateActionData = async (selectedServices) => {
    const actionData = getValues('actionData');
    if (actionData?.length) {
      const selectedServiceIds = new Set(selectedServices.map((service) => service.id));

      const updatedActionData = actionData.map((action) => {
        if (action.selectedServices?.length) {
          // Filter out services that are no longer in the selected services list
          const updatedServices = action.selectedServices.filter((service) => selectedServiceIds.has(service.id));

          return {
            ...action,
            selectedServices: updatedServices,
          };
        }
        return action;
      });

      setValue('actionData', updatedActionData);
      updatedActionData.forEach((_, index) => {
        trigger(`actionData[${index}].selectedServices`);
      });
    }
  };

  const handleToggleOption = async (selectedOptions) => {
    setSelectedOptions(selectedOptions);
    handleSelectServices(selectedOptions);
    setValue('services', selectedOptions);
    await trigger('services');
    await updateActionData(selectedOptions);
  };

  const handleClearOptions = async () => {
    setSelectedOptions([]);
    handleSelectServices([]);
    setValue('services', []);
    await trigger('services');
    await updateActionData([]);
  };

  const handleSelectAll = async (isSelected, field) => {
    if (isSelected) {
      field.onChange(availableServicesList);
      setSelectedOptions(availableServicesList);
      handleSelectServices(availableServicesList);
      setValue('services', availableServicesList);
      await trigger('services');
      await updateActionData(availableServicesList);
    } else {
      field.onChange([]);
      handleClearOptions();
    }
  };

  return (
    <>
      <MultiSelect
        fieldName="services"
        items={availableServicesList}
        getOptionLabel={(option) => option?.display}
        selectedValues={selectedOptions}
        placeholder={strings.servicesPlaceholder}
        selectAllLabel="Select all"
        onToggleOption={handleToggleOption}
        onClearOptions={handleClearOptions}
        onSelectAll={handleSelectAll}
        validationData={validationData}
        validationError={errors?.services?.message || ''}
      />
    </>
  );
};
