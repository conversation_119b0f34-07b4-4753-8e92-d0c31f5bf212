import React from 'react';
import { Box, Paper, Typography } from "@mui/material";
import { HeadingAndDescription } from "@/components/HeadingAndDescription";
import strings from "@/utils/localization";
import { CambianTooltip } from '@/components';

export const Discard = (props) => {
  const { validationData } = props;
  const { register, errors, control } = validationData;

  return (
    <Box>
      <Typography sx={{ mt: 2, mb: 2 }}>
        <CambianTooltip
          placement="right"
          title={
            <Typography variant="caption">
              {strings.discardPageTooltip}
            </Typography>
          }
        >
          <span>{strings.discardPage}</span>
        </CambianTooltip>
      </Typography>
      <Paper
        sx={{
          my: 1,
          border: "1px solid #F0F0F0",
          borderRadius: "5px 5px 0 0",
          px: { xs: 2, sm: 3 },
          py: 2,
        }}
      >
        <HeadingAndDescription
          headingName={"discardHeading"}
          descriptionName={"discardDescription"}
          register={register}
          errors={errors}
          control={control}
        />
      </Paper>
    </Box>
  );
};
