# WidgetEditor Documentation

## Overview

The WidgetEditor is a React component designed to facilitate the management and manipulation of widgets. It provides a user interface with various functionalities, creating, updating, deleting different kind of widgets like Booking Widgets, Questionnaire widgets and Registration widgets. It also functionalities import a widget using JSON file and exporting a widget in JSON format. Additionally, it provides a option to preview widget and also provide the functionality to get widget embed code.

## Usage

To integrate the QuestionnaireEditorKit into your React application, follow these steps:

1. Import the component:

   ```javascript
   import { WidgetEditor } from '@github_username/cambianwe';
   ```

2. Include the component in your JSX code:

   ```javascript
   <WidgetEditor
     // common props
     widgetsList={allWidgets}
     organizationId={orgId}
     widgetBaseUrl={VITE_WIDGET_BASE_URL}
     handleImportWidgetCallback={handleImportWidgetCallback}
     handlePreviewWidgetCallback={handlePreviewWidgetCallback}
     handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
     handleDeleteWidgetCallback={handleDeleteWidgetCallback}
     handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWidgetCallback}
     allIdTypes={allIdTypes}
     orgRequiredIdTypes={demographicFields?.idTypes}
     // booking props
     bookingWidgetFields={demographicFields?.bookingWidgetFields}
     servicesList={servicesList}
     locationsList={locationsList}
     // questionnaire props
     questionnaireWidgetFields={demographicFields?.questionnaireWidgetFields}
     publicQuestionnaireList={publicQuestionnaireList}
     privateQuestionnaireList={privateQuestionnaireList}
     handleQuestionnaireDropdownCallback={handleQuestionnaireDropdown}
     questionnaireDefinition={questionnaireDefinition}
     setQuestionnaireDefinition={setQuestionnaireDefinition}
     // registration props
     registrationWidgetFields={demographicFields?.registrationWidgetFields}
   />
   ```

The WidgetEditor expects several variables and callback functions to manage different actions related to widgets. Here are the functions you need to implement:

## Common Props

Common Props

1. **widgetsList** (Type: Array)
   - **Description**: An array containing all the widgets to be managed by the WidgetEditor.
   - **Example**:
   ```jsx
   widgetsList = { allWidgets };
   ```
2. **organizationId** (Type: String)

   - **Description**: A string that denotes the Organization Id.
   - **Example**: '6ae77cdb-a71c-4753-a8d4-b8f6fa632f53'

3. **widgetBaseUrl** (Type: String)

   - **Description**: A string that denotes the base URL for the widget.
   - **Example**: 'https://develop.d2yc83ebf3vw6y.amplifyapp.com/'

4. **handleImportWidgetCallback** (Type: Function)

   - **Description**: Callback function triggered when the import button is clicked.
   - **Parameters**:
     - **widgetData**: Data of the widget being created or updated.
     - **widgetType**: Type of the widget being created or updated.
   - **Return Value**: Object with a 'success' property of type boolean ({ success: true/false }).

5. **handlePreviewWidgetCallback** (Type: Function)

   - **Description**: Callback function triggered when the preview button for a widget is clicked.
   - **Parameters**: None
   - **Return Value**: None

6. **handleDuplicateWidgetCallback** (Type: Function)

   - **Description**: Callback function triggered when the duplicate button for a widget is clicked.
   - **Parameters**:
     - **widgetData**: Data of the widget being duplicated.
     - **widgetType**: Type of the widget being duplicated.
   - **Return Value**: Object with a 'success' property of boolean type ({ success: true/false }).

7. **handleDeleteWidgetCallback** (Type: Function)

   - **Description**: Callback function triggered when the delete button for a widget is clicked.
   - **Parameters**:
     - **widgetData**: Data of the widget being deleted.
     - **widgetType**: Type of the widget being deleted.
   - **Return Value**: None

8. **handleSaveOrUpdateWidgetCallback** (Type: Function)

   - **Description**: Callback function triggered when creating or updating any type of widget.
   - **Parameters**:
     - **widgetData**: Data of the widget being created or updated.
     - **widgetType**: Type of the widget being created or updated.
   - **Return Value**: None

9. **allIdTypes** (Type: Array<object>)

   - **Description**: An array of objects containing all the IdTypes possible. It comes from the API that defines all possible id types and its subsequent issuers.

10. **orgRequiredIdTypes** (Type: Array<object>)
    - **Description**: An array of object containing all the IdTypes and its issuers that are defined by the Organization. It comes via the Org API.

## Booking Widget Props

Booking Widget Props

11. **bookingWidgetFields** (Type: Array<object>)

- **Description**: Fields used specifically for booking widgets.

12. **servicesList** (Type: Array<object>)

- **Description**: List of services required in booking widgets.

13. **locationsList** (Type: Array<object>)

- **Description**: List of locations required in booking widgets.

## Questionnaire Widget Props

Questionnaire Widget Props

14. **questionnaireWidgetFields** (Type: Object)

- **Description**: Fields used specifically for questionnaire widgets.

15. **publicQuestionnaireList** (Type: Array<object>)

- **Description**: List of questionnaires that are published in the public repository.

16. **privateQuestionnaireList** (Type: Array<object>)

- **Description**: List of questionnaires that are published in the private repository.

17. **handleQuestionnaireDropdownCallback** (Type: Function)

- **Description**: Callback function triggered when a questionnaire is selected from the dropdown.
- **Parameters**:
  - **questionnaireId**: Identifier of the selected questionnaire.
- **Return Value**: None

18. **questionnaireDefinition** (Type: Object)

- **Description**: Selected questionnaire definition in FHIR format. Stores the definition of the questionnaire whose ID is received in handleQuestionnaireDropdownCallback.

19. **setQuestionnaireDefinition** (Type: Object)

- **Description**:Get the questionnaire from the private/public repository and set the questionnaire definition accordingly.

## Registration Widget Props

Registration Widget Props

20. **registrationWidgetFields** (Type: Array<object>)

- **Description**: Fields used specifically for registration widgets.

## Example Implementation

```javascript
import React from 'react';
import WidgetEditor from 'path/to/QuestionnaireEditorKit';

const YourComponent = () => {
  // Define your questionnaireList and callback functions here

  return (
    <WidgetEditor
      widgetsList={allWidgets}
      organizationId={orgId}
      widgetBaseUrl={VITE_WIDGET_BASE_URL}
      handleImportWidgetCallback={handleImportWidgetCallback}
      handlePreviewWidgetCallback={handlePreviewWidgetCallback}
      handleDuplicateWidgetCallback={handleDuplicateWidgetCallback}
      handleDeleteWidgetCallback={handleDeleteWidgetCallback}
      handleSaveOrUpdateWidgetCallback={handleSaveOrUpdateWidgetCallback}
      allIdTypes={allIdTypes}
      orgRequiredIdTypes={demographicFields?.idTypes}
      bookingWidgetFields={demographicFields?.bookingWidgetFields}
      servicesList={servicesList}
      locationsList={locationsList}
      questionnaireWidgetFields={demographicFields?.questionnaireWidgetFields}
      publicQuestionnaireList={publicQuestionnaireList}
      privateQuestionnaireList={privateQuestionnaireList}
      handleQuestionnaireDropdownCallback={handleQuestionnaireDropdown}
      questionnaireDefinition={questionnaireDefinition}
      setQuestionnaireDefinition={setQuestionnaireDefinition}
      registrationWidgetFields={demographicFields?.registrationWidgetFields}
    />
  );
};

export default YourComponent;
```

`For more details and examples, refer to the ./src/TestPage/index.jsx file in your project.`
