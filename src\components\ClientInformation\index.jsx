import React, { useState } from 'react';
import {
  FormHelperText,
  Grid,
  Paper,
  Switch,
  TextField,
  Typography,
  Box,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import { HeadingAndDescription } from '@/components/HeadingAndDescription';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { ClientInformationFields } from '@/components/ClientInformationFields';
import { IDENTIFIED, UNIDENTIFIED } from '@/utils/constants';

export const ClientInformation = (props) => {
  const {
    validationData,
    fieldsValidationError,
    handleSelectFields,
    existingWidgetData,
    selectedIdTypes,
    setSelectedIdTypes,
    allIdTypes,
    orgRequiredIdTypes,
    setIsDemographicsEnabled,
    allowedIdentifications,
    fieldsList,
    setFieldsList,
  } = props;

  const { control, errors, register, setValue, getValues } = validationData;
  const { byRequestOnly } = existingWidgetData || {};

  const [identification, setIdentification] = useState(getValues('identification') || IDENTIFIED);
  const [isByRequestOnly, setIsByRequestOnly] = useState(byRequestOnly || getValues('byRequestOnly') || false);

  const handleIdentificationChange = (event) => {
    const value = event.target.value;
    setIdentification(value);
    if (value === IDENTIFIED) {
      setIsDemographicsEnabled(true);
      setValue('isDemographicsEnabled', true);
    } else {
      setIsDemographicsEnabled(false);
      setValue('isDemographicsEnabled', false);
    }
  };

  return (
    <>
      <Grid container alignItems="center" sx={{ mt: 1, pr: { xs: 2, sm: 0 } }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.identificationTooltip}</Typography>}
            >
              <span>{strings.identification}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            name="identification"
            control={control}
            render={({ field: { value, ...field } }) => (
              <RadioGroup
                row
                name="identification"
                value={identification}
                onChange={(e) => {
                  handleIdentificationChange(e);
                  setValue('identification', e.target.value);
                  field.onChange(e.target.value);
                }}
              >
                {allowedIdentifications.map((option) => (
                  <FormControlLabel key={option.value} label={option.label} value={option.value} control={<Radio />} />
                ))}
              </RadioGroup>
            )}
          />
        </Grid>
      </Grid>

      {identification != UNIDENTIFIED && (
        <Grid>
          {identification === IDENTIFIED && (
            <>
              <Grid container alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={4} lg={3}>
                  <Typography>
                    <span>{strings.title}</span>
                  </Typography>
                </Grid>
                <Grid item xs={8} lg={9}>
                  <Controller
                    control={control}
                    name="clientInformationPageTitle"
                    render={({ field: { ref, ...field } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        size="small"
                        autoComplete="off"
                        placeholder={strings.personalInformation}
                        type="text"
                        name="clientInformationPageTitle"
                        onChange={(event) => {
                          if (event.target.value.trim()) {
                            field.onChange(event.target.value);
                          } else {
                            field.onChange(event.target.value.trim());
                          }
                        }}
                      />
                    )}
                  />
                </Grid>
              </Grid>

              <Grid container alignItems="center" sx={{ mt: 2 }}>
                <Grid item xs={4} lg={3}>
                  <Typography>
                    <span>{strings.subTitle}</span>
                  </Typography>
                </Grid>
                <Grid item xs={8} lg={9}>
                  <Controller
                    control={control}
                    name="clientInformationPageSubtitle"
                    render={({ field: { ref, ...field } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        size="small"
                        autoComplete="off"
                        type="text"
                        name="clientInformationPageSubtitle"
                        onChange={(event) => {
                          if (event.target.value.trim()) {
                            field.onChange(event.target.value);
                          } else {
                            field.onChange(event.target.value.trim());
                          }
                        }}
                        error={!!errors?.demographicPageHeading}
                        helperText={errors?.demographicPageHeading?.message}
                      />
                    )}
                  />
                </Grid>
              </Grid>
              <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
                {strings.items}
              </Typography>
              <Paper
                sx={{
                  my: 2,
                  p: 2,
                  border: '1px solid #F0F0F0',
                  borderRadius: '5px 5px 0 0',
                  borderColor: fieldsValidationError ? '#f44336' : '#F0F0F0',
                }}
              >
                <ClientInformationFields
                  fieldType="demographics"
                  fieldsList={fieldsList}
                  setFieldsList={setFieldsList}
                  handleSelectFields={handleSelectFields}
                  validationData={validationData}
                  selectedIdTypes={selectedIdTypes}
                  setSelectedIdTypes={setSelectedIdTypes}
                  allIdTypes={allIdTypes}
                  orgRequiredIdTypes={orgRequiredIdTypes}
                />
              </Paper>

              {fieldsValidationError && <FormHelperText error>{fieldsValidationError}</FormHelperText>}
              <Grid container alignItems="center" sx={{ my: 2 }}>
                <Grid item xs={4} lg={3}>
                  <Typography>
                    <CambianTooltip
                      placement="right"
                      title={<Typography variant="caption">{strings.verifyWithOTPTooltip}</Typography>}
                    >
                      <span>{strings.OTPVerification}</span>
                    </CambianTooltip>
                  </Typography>
                </Grid>
                <Grid item xs={8} lg={9}>
                  <Controller
                    control={control}
                    name="isOtpVerificationChecked"
                    render={({ field: { ref, ...field } }) => {
                      return (
                        <Switch
                          id="otp-verification-toggle"
                          size="small"
                          checked={field?.value !== undefined ? field?.value : false}
                          onChange={(event, val) => {
                            return field?.onChange(val);
                          }}
                        />
                      );
                    }}
                  />
                </Grid>
              </Grid>
            </>
          )}
          <Grid container alignItems="center" sx={{ my: 2 }}>
            <Grid item xs={4} lg={3}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.selfRegistrationTooltip}</Typography>}
              >
                <span>{strings.selfRegistration}</span>
              </CambianTooltip>
            </Grid>
            <Grid item xs={8} lg={9}>
              <Controller
                control={control}
                name="selfRegister"
                render={({ field: { ref, ...field } }) => {
                  return (
                    <Switch
                      id="self-registration-toggle"
                      size="small"
                      checked={field?.value !== undefined ? field?.value : false}
                      onChange={(event, val) => {
                        return field?.onChange(val);
                      }}
                    />
                  );
                }}
              />
            </Grid>
          </Grid>

          <Box>
            <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.individualNotFoundPageTooltip}</Typography>}
              >
                <span>{strings.individualNotFound}</span>
              </CambianTooltip>
            </Typography>
            <Paper
              sx={{
                my: 1,
                border: '1px solid #F0F0F0',
                borderRadius: '5px 5px 0 0',
                px: { xs: 2, sm: 3 },
                py: 2,
              }}
            >
              <HeadingAndDescription
                register={register}
                control={control}
                errors={errors}
                headingName="individualNotFoundPage.heading"
                descriptionName="individualNotFoundPage.description"
                headingPlaceholder={strings.clientNotFound}
                descriptionPlaceholder={strings.pleaseGetInTouchWithUs}
                enableButton={false}
              />
            </Paper>
          </Box>

          <Grid container alignItems="center" sx={{ mt: 3 }}>
            <Grid item xs={4} lg={3}>
              <Typography>
                <CambianTooltip
                  placement="right"
                  title={<Typography variant="caption">{strings.byRequestOnlyTooltip}</Typography>}
                >
                  <span>{strings.byRequestOnly}</span>
                </CambianTooltip>
              </Typography>
            </Grid>
            <Grid item xs={8} lg={9}>
              <Controller
                control={control}
                name="byRequestOnly"
                render={({ field: { ref, ...field } }) => {
                  return (
                    <Switch
                      size="small"
                      name="byRequestOnly"
                      checked={field?.value !== undefined ? field?.value : false}
                      onChange={(event, val) => {
                        setIsByRequestOnly(val);
                        return field?.onChange(val);
                      }}
                    />
                  );
                }}
              />
            </Grid>
          </Grid>

          {isByRequestOnly && (
            <Box>
              <Typography variant="body1" sx={{ mt: 2, mb: 2 }}>
                <CambianTooltip
                  placement="right"
                  title={<Typography variant="caption">{strings.requestNotFoundTooltip}</Typography>}
                >
                  <span>{strings.requestNotFound}</span>
                </CambianTooltip>
              </Typography>
              <Paper
                sx={{
                  my: 1,
                  border: '1px solid #F0F0F0',
                  borderRadius: '5px 5px 0 0',
                  px: { xs: 2, sm: 3 },
                  py: 2,
                }}
              >
                <HeadingAndDescription
                  register={register}
                  control={control}
                  errors={errors}
                  headingName="requestNotFoundPage.heading"
                  descriptionName="requestNotFoundPage.description"
                  headingPlaceholder={strings.requestNotFound}
                  descriptionPlaceholder={strings.pleaseGetInTouchWithUs}
                  enableButton={false}
                />
              </Paper>
            </Box>
          )}
        </Grid>
      )}
    </>
  );
};
