import React from 'react';
import { Typography, Dialog, DialogContent, DialogTitle } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

export default function ModalComponent({ modalOpen, onClose, mainContent, title, width }) {
  return (
    <>
      <Dialog fullWidth={true} maxWidth={width ? width : 'sm'} open={modalOpen} onClose={onClose} scroll="body">
        <DialogTitle>
          <Typography variant={'h6'} sx={{ color: 'text.secondary', pr: 1 }}>
            {title}
          </Typography>
          {onClose ? (
            <IconButton
              aria-label="close"
              onClick={onClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          ) : null}
        </DialogTitle>
        <DialogContent>{mainContent}</DialogContent>
      </Dialog>
    </>
  );
}
