import React, { useEffect, useState } from 'react';
import { FormHelperText, Grid, Paper, Switch, TextField, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { getAvailableFieldsList } from '@/utils/commonUtility';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { ClientInformationFields } from '@/components/ClientInformationFields';

export const DemographicsAndContact = (props) => {
  const {
    validationData,
    fieldsValidationError,
    handleSelectFields,
    bookingWidgetFields,
    existingWidgetData,
    selectedIdTypes,
    setSelectedIdTypes,
    allIdTypes,
    orgRequiredIdTypes,
  } = props;

  const { control, errors } = validationData;

  const { fields = [], provinces, phnDisplayName } = existingWidgetData || {};

  const [fieldsList, setFieldsList] = useState(getAvailableFieldsList(bookingWidgetFields, fields, phnDisplayName));

  useEffect(() => {
    setFieldsList(getAvailableFieldsList(bookingWidgetFields, fields));
  }, [bookingWidgetFields]);

  const [selectedJurisdictions, setSelectedJurisdictions] = useState(provinces || []);

  return (
    <>
      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item lg="auto">
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.clientInformationTooltip}</Typography>}
            >
              <span>{strings.clientInformation}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.clientInformationPageTitleTooltip}</Typography>}
            >
              <span>{strings.title}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="clientInformationPageTitle"
            render={({ field: { ref, ...field } }) => (
              <TextField
                {...field}
                fullWidth
                size="small"
                autoComplete="off"
                placeholder={strings.title}
                type="text"
                name="clientInformationPageTitle"
                onChange={(event) => {
                  if (event.target.value.trim()) {
                    field.onChange(event.target.value);
                  } else {
                    field.onChange(event.target.value.trim());
                  }
                }}
                sx={{ maxWidth: '100%' }}
              />
            )}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.clientInformationPageSubTitleTooltip}</Typography>}
            >
              <span>{strings.subTitle}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="clientInformationPageSubtitle"
            render={({ field: { ref, ...field } }) => (
              <TextField
                {...field}
                fullWidth
                size="small"
                autoComplete="off"
                placeholder={strings.subTitle}
                type="text"
                name="clientInformationPageSubtitle"
                onChange={(event) => {
                  if (event.target.value.trim()) {
                    field.onChange(event.target.value);
                  } else {
                    field.onChange(event.target.value.trim());
                  }
                }}
                error={!!errors?.demographicPageHeading}
                helperText={errors?.demographicPageHeading?.message}
                sx={{ maxWidth: '100%' }}
              />
            )}
          />
        </Grid>
      </Grid>
      <Paper
        sx={{
          my: 2,
          p: 2,
          border: '1px solid #F0F0F0',
          borderRadius: '5px 5px 0 0',
          borderColor: fieldsValidationError ? '#f44336' : '#F0F0F0',
        }}
      >
        <ClientInformationFields
          fieldType="demographics"
          fieldsList={fieldsList}
          setFieldsList={setFieldsList}
          handleSelectFields={handleSelectFields}
          validationData={validationData}
          selectedJurisdictions={selectedJurisdictions}
          setSelectedJurisdictions={setSelectedJurisdictions}
          selectedIdTypes={selectedIdTypes}
          setSelectedIdTypes={setSelectedIdTypes}
          allIdTypes={allIdTypes}
          orgRequiredIdTypes={orgRequiredIdTypes}
        />
      </Paper>
      {fieldsValidationError && <FormHelperText error>{fieldsValidationError}</FormHelperText>}
      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.verifyWithOTPTooltip}</Typography>}
            >
              <span>{strings.verifyWithOTP}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isOtpVerificationChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  checked={field?.value !== undefined ? field?.value : false}
                  onChange={(event, val) => {
                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.allowMultipleClientsTooltip}</Typography>}
            >
              <span>{strings.allowMultipleClients}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isMultipleIndividualChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  checked={field?.value !== undefined ? field?.value : false}
                  onChange={(event, val) => {
                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>
    </>
  );
};
