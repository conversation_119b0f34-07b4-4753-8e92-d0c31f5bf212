import React from 'react';
import { Menu, MenuItem } from '@mui/material';
import { pages } from '@/utils/constants/common';
import { BOOKING_CAPS, QUESTIONNAIRE_CAPS, REGISTRATION_CAPS } from '@/utils/constants';

export const WidgetTypeMenu = (props) => {
  const { anchorEl, handleClose, handleNavigationCallback } = props;

  const widgetTypes = [
    {
      id: 'WIDGET_BOOKING',
      label: 'Booking',
      onClick: () => {
        handleNavigationCallback(pages.addNewBookingWidget, BOOKING_CAPS);
      },
    },
    {
      id: 'WIDGET_QUESTIONNAIRE',
      label: 'Questionnaire',
      onClick: () => {
        handleNavigationCallback(pages.addNewQuestionnaireWidget, QUESTIONNAIRE_CAPS);
      },
    },
    {
      id: 'WIDGET_REGISTRATION',
      label: 'Registration',
      onClick: () => {
        handleNavigationCallback(pages.addNewRegistrationWidget, REGISTRATION_CAPS);
      },
    },
  ];

  return (
    <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
      {widgetTypes.map((widget) => (
        <MenuItem
          key={widget.id}
          onClick={() => {
            handleClose();
            widget.onClick();
          }}
        >
          {widget.label}
        </MenuItem>
      ))}
    </Menu>
  );
};
