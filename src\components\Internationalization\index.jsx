import React, { useEffect, useState, useRef } from 'react';
import { Grid, Typography, Select, MenuItem, FormHelperText, Button, Link } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { getSampleTranslationStructure } from '@/utils/languageService';
import { downloadFileInJsonFormat } from '@/utils/commonUtility';
import { FileUpload } from '@mui/icons-material';

export const Internationalization = (props) => {
  const { validationData, existingWidgetData, handleWidgetLanguageChange } = props;
  const { errors, control, getValues, watch, setValue } = validationData;

  const currentLanguage = watch('currentLanguage');
  const defaultLanguage = watch('defaultLanguage');

  const previousDefaultLanguage = useRef(defaultLanguage);
  const [languages, setLanguages] = useState([
    { value: 'en', label: strings.english || 'English' },
    { value: 'fr', label: strings.french || 'Français' },
  ]);

  useEffect(() => {
    if (currentLanguage) {
      strings.setActiveLanguage(currentLanguage);
      setLanguages([
        { value: 'en', label: strings.english || 'English' },
        { value: 'fr', label: strings.french || 'Français' },
      ]);
    }
  }, [currentLanguage]);
  useEffect(() => {
    if (defaultLanguage && !existingWidgetData?.SK) {
      if (previousDefaultLanguage.current !== defaultLanguage) {
        setValue('currentLanguage', defaultLanguage);
        if (handleWidgetLanguageChange) {
          handleWidgetLanguageChange(defaultLanguage, false);
        }
      }
      previousDefaultLanguage.current = defaultLanguage;
    }
  }, [defaultLanguage, setValue, existingWidgetData, handleWidgetLanguageChange]);

  const handleDownloadSample = (e) => {
    e.preventDefault();
    const sampleStructure = getSampleTranslationStructure(existingWidgetData?.widgetType);
    const formattedSampleStructure = {
      en: {},
      fr: {},
    };

    Object.entries(sampleStructure).forEach(([key, value]) => {
      if (value.en) formattedSampleStructure.en[key] = value.en;
      if (value.fr) formattedSampleStructure.fr[key] = value.fr;

      if (typeof value === 'object' && !value.en && !value.fr) {
        formattedSampleStructure.en[key] = {};
        formattedSampleStructure.fr[key] = {};

        Object.entries(value).forEach(([nestedKey, nestedValue]) => {
          if (nestedValue.en) formattedSampleStructure.en[key][nestedKey] = nestedValue.en;
          if (nestedValue.fr) formattedSampleStructure.fr[key][nestedKey] = nestedValue.fr;
        });
      }
    });

    downloadFileInJsonFormat(JSON.stringify(formattedSampleStructure, null, 2), `translation-sample json`);
  };

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const translations = JSON.parse(e.target.result);
          // Handle the import here
          console.log('Importing translations:', translations);
        } catch (error) {
          console.error('Error parsing JSON file:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <>
      <Grid container spacing={2}>
        <Grid container item alignItems="center">
          <Grid item xs={4} lg={3}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.defaultLanguageTooltip}</Typography>}
            >
              {strings.defaultLanguage}
            </CambianTooltip>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              control={control}
              name="defaultLanguage"
              defaultValue={'en'}
              render={({ field }) => (
                <Select {...field} fullWidth size="small" error={!!errors?.defaultLanguage}>
                  {languages.map((lang) => (
                    <MenuItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            {errors?.defaultLanguage && <FormHelperText error>{errors.defaultLanguage.message}</FormHelperText>}
          </Grid>
        </Grid>

        <Grid container item alignItems="center" sx={{ mt: 2 }}>
          <Grid item xs={4} lg={3}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.importi18nFileTooltip}</Typography>}
            >
              {strings.importi18nFile}
            </CambianTooltip>
          </Grid>
          <Grid item xs={8} lg={9}>
            <input
              type="file"
              accept=".json"
              onChange={handleFileImport}
              style={{ display: 'none' }}
              id="i18n-import"
            />
            <label htmlFor="i18n-import">
              <Button
                variant="outlined"
                component="span"
                startIcon={<FileUpload />}
                sx={{
                  minWidth: '120px',
                  width: '120px',
                  height: '40px',
                  p: 0,
                  '& .MuiButton-startIcon': {
                    margin: 0,
                  },
                }}
              />
            </label>
            <Link href="#" onClick={handleDownloadSample} sx={{ ml: 2 }}>
              {strings.downloadSampleStructure}
            </Link>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};
