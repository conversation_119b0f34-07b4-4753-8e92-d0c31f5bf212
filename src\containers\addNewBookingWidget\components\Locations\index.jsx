import React, { useMemo, useState } from 'react';
import { Grid, Stack, Typography } from '@mui/material';
import { GoogleMap, ModalComponent } from '@/components';
import strings from '@/utils/localization';
import { BASE64_IMAGE, COMPARE_IMAGE_URL } from '@/utils/constants/index';
import noImageIcon from '@/assets/images/no-image.jpg';
import EmailIcon from '@mui/icons-material/Email';
import PinDropIcon from '@mui/icons-material/PinDrop';
import { sortLists } from '@/utils/commonUtility';
import { MultiSelect } from './MultiSelect';

function locationTitle(text) {
  return (
    <Typography variant={'h6'} sx={{ color: 'text.secondary' }}>
      {text}
    </Typography>
  );
}

export const Locations = (props) => {
  const { validationData, existingWidgetData, availableLocations } = props;

  const { locations } = existingWidgetData || {};
  const { errors, getValues, setValue, trigger } = validationData;

  const [openSnippetModal, setOpenSnippetModal] = useState(false);
  const [locationDetails, setLocationDetails] = useState({});
  const [obj, setObj] = useState({});
  const [selectedOptions, setSelectedOptions] = useState(() => getValues('locations') || locations || []);

  const availableLocationsList = availableLocations?.sort(sortLists);

  useMemo(() => {
    const formLocations = getValues('locations');
    if (formLocations?.length) {
      setSelectedOptions(formLocations);
    } else if (existingWidgetData?.locations?.length) {
      setSelectedOptions(existingWidgetData.locations);
      setValue('locations', existingWidgetData.locations);
    }
  }, [existingWidgetData]);

  //open snippet popup
  const handleLocationDetails = (location) => {
    let line1 = '';
    let tele = '';
    let address = '';
    let image = '';

    location.telecom.map((val) => (tele = val.value ? val.value + ' ' : ''));

    if ('address' in location) {
      location.address.line.map((value) => (line1 = line1 + value + ', '));

      let city = 'city' in location.address ? location.address.city : '';
      let state = 'state' in location.address ? ', ' + location.address.state : '';
      let country = 'country' in location.address ? ', ' + location.address.country : '';
      let postalCode = 'postalCode' in location.address ? ', ' + location.address.postalCode : '';

      address = line1 + city + state + country + postalCode;
    }

    if ('extension' in location) {
      location.extension.map((val) => (image = val.url === COMPARE_IMAGE_URL ? val.valueString : ''));
    }

    let modalData = {
      address: address,
      name: location.name,
      telecom: tele,
      position: location.position,
      image: image,
    };

    setLocationDetails(modalData);
    setOpenSnippetModal(true);
  };

  //close snippet popup
  const closeSnippetModal = () => {
    setOpenSnippetModal(false);
  };

  const handleToggleOption = async (selectedOptions) => {
    setSelectedOptions(selectedOptions);
    setValue('locations', selectedOptions);
    await trigger('locations');
  };

  const handleClearOptions = async () => {
    setSelectedOptions([]);
    setValue('locations', []);
    await trigger('locations');
  };

  const handleSelectAll = async (isSelected, field) => {
    if (isSelected) {
      field.onChange(availableLocationsList);
      setSelectedOptions(availableLocationsList);
      setValue('locations', availableLocationsList);
      await trigger('locations');
    } else {
      field.onChange([]);
      handleClearOptions();
    }
  };

  //snippet
  const getLocationDetails = (
    <Grid container sx={{ overflowX: 'hidden' }}>
      <Grid item sm={12} sx={{ mb: 2 }}>
        <img
          src={locationDetails.image ? BASE64_IMAGE + locationDetails.image : noImageIcon}
          width="250"
          height="250"
          alt={strings.na}
        />
      </Grid>
      <Grid item sm={12} sx={{ mb: 2 }}>
        <Stack direction="row">
          <PinDropIcon sx={{ mr: 1, color: 'text.subHeading' }}></PinDropIcon>
          <Typography>{locationDetails.address}</Typography>
        </Stack>
      </Grid>
      <Grid item sm={12} sx={{ mb: 2 }}>
        <Stack direction="row">
          <EmailIcon sx={{ mr: 1, color: 'text.subHeading' }}></EmailIcon>
          <Typography>{locationDetails.telecom ? locationDetails.telecom : strings.na}</Typography>
        </Stack>
      </Grid>
      <Grid item sm={12} sx={{ p: 0 }}>
        <GoogleMap address={locationDetails} />
      </Grid>
    </Grid>
  );

  return (
    <div id="locations-dropdown">
      <MultiSelect
        items={availableLocationsList}
        getOptionLabel={(option) => option.name}
        getOptionDisabled={(option) => option.name === 'Child'}
        selectedValues={selectedOptions}
        placeholder={strings.locationPlaceholder}
        selectAllLabel="Select all"
        onToggleOption={handleToggleOption}
        onClearOptions={handleClearOptions}
        onSelectAll={handleSelectAll}
        onClickOfSetting={handleLocationDetails}
        validationData={validationData}
        validationError={errors?.services?.message || ''}
      />
      <ModalComponent
        modalOpen={openSnippetModal}
        onClose={closeSnippetModal}
        mainContent={getLocationDetails}
        title={locationTitle(locationDetails.name)}
      />
    </div>
  );
};
