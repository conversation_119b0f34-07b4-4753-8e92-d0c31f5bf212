import * as yup from 'yup';
import { EMAIL, PHONE } from '@/utils/constants';
import strings from '@/utils/localization';

const getMessage = (key) => () => strings[key];

export const schema = yup.object({
  name: yup.string().required(getMessage('widgetNameRequired')).min(2, getMessage('minCharLength2')),
  defaultLanguage: yup.string().required(getMessage('defaultLanguageRequired')),
  identification: yup.string().required(getMessage('identificationRequired')),
  finalPageHeading: yup.string().required(getMessage('headingRequired')),
  finalPageDescription: yup.string().required(getMessage('descriptionRequired')),
  registerButtonText: yup.string(),
  isIntroChecked: yup.boolean(),
  introHeading: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('headingRequired')).min(2, getMessage('minCharLength2')),
  }),
  introDescription: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('descriptionRequired')).min(2, getMessage('minCharLength2')),
  }),
  isActionEnabled: yup.boolean(),
  actionButtonText: yup.string(),
  actionData: yup.array().when('isActionEnabled', {
    is: true,
    then: () =>
      yup.array(
        yup.object({
          selectedAction: yup.string().required(getMessage('actionRequired')),
          actionHeading: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('headingRequired')),
          }),
          actionDescription: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('descriptionRequired')),
          }),
          backgroundServiceEndpoint: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Service',
            then: () => yup.string().url(getMessage('apiEndpointValid')).required(getMessage('apiEndpointRequired')),
          }),
          redirectUrl: yup.string().when('selectedAction', {
            is: (value) => value && value === 'URL',
            then: () => yup.string().url(getMessage('validURL')).required(getMessage('redirectUrlRequired')),
          }),
          selectedTarget: yup.string().when('selectedAction', {
            is: (value) => value && (value === 'URL' || value === 'Widget'),
            then: () => yup.string().required(getMessage('targetRequired')),
          }),
          selectedWidgetType: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Widget',
            then: () => yup.string().required(getMessage('widgetTypeRequired')),
          }),
          selectedWidget: yup
            .object()
            .shape()
            .when('selectedAction', {
              is: (value) => value && value === 'Widget',
              then: () =>
                yup.object({
                  name: yup.string().required(getMessage('widgetRequired')),
                }),
            }),
        }),
      ),
  }),
});

export const fieldsValidation = yup.array().of(
  yup.object().shape({
    code: yup.string().required(),
  }),
);
