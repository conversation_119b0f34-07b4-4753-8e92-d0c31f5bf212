import React, { Fragment, useEffect, useState } from 'react';
import IconButton from '@mui/material/IconButton';
import { Clear } from '@mui/icons-material';
import { Typography } from '@mui/material';
import { useSnackbar } from 'notistack';

export const useNotification = () => {
  const [conf, setConf] = useState({});
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const action = (key) => (
    <Fragment>
      <IconButton
        onClick={() => {
          closeSnackbar(key);
        }}
      >
        <Clear fontSize="small" sx={{ color: '#fff' }} />
      </IconButton>
    </Fragment>
  );

  useEffect(() => {
    if (conf?.msg) {
      let variant = 'info';
      if (conf.variant) {
        variant = conf.variant;
      }
      enqueueSnackbar(<Typography sx={{ pl: 1 }}>{conf.msg}</Typography>, {
        variant: variant,
        autoHideDuration: 3000,
        action,
      });
      setConf({ variant: '', msg: null });
    }
  }, [conf]);

  return [setConf];
};
