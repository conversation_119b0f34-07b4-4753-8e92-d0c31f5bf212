import React, { useState, useMemo } from 'react';
import {
  Box,
  Grid,
  TextField,
  Typography,
  Switch,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  RadioGroup,
  Radio,
  Paper,
  Autocomplete,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { repositoryTypes } from '@/utils/constants';
import { PrintAndDownloadConfigurations } from '../component/PrintAndDownloadConfigurations';
import { HeadingAndDescription } from '@/components/HeadingAndDescription';

export const QuestionnaireSettings = ({
  validationData,
  publicQuestionnaireList,
  privateQuestionnaireList,
  handleQuestionnaireDropdownCallback,
  setQuestionnaireDefinition,
  existingWidgetData,
  isReportPageEnabled,
  setIsReportPageEnabled,
  enableLogin,
  isDynamicWidget,
  setIsDynamicWidget,
}) => {
  const { register, errors, control, setValue, actionData, getValues } = validationData;
  const { showProgressBar, repository } = existingWidgetData || {};

  const [progressBarEnabled, setProgressBarEnabled] = useState(showProgressBar || false);
  const [selectedRepository, setSelectedRepository] = useState(repository ? repository : getValues('repository') ?? '');
  const [questionnaireSearchKey, setQuestionnaireSearchKey] = useState('');

  const questionnaireList = useMemo(() => {
    if (selectedRepository === repositoryTypes.private) {
      return privateQuestionnaireList;
    } else if (selectedRepository === repositoryTypes.public) {
      return publicQuestionnaireList;
    } else {
      return [{ shortName: 'Select repository first', artifact: 0 }];
    }
  }, [selectedRepository, publicQuestionnaireList, privateQuestionnaireList]);

  const handleGetScoreNameList = (questionnaireId) => {
    if (questionnaireId) {
      handleQuestionnaireDropdownCallback(selectedRepository, questionnaireId);
    } else {
      setQuestionnaireDefinition();
    }
    for (let index in actionData) {
      setValue(`actionData[${index}].scoreDefinitionName`, '');
    }
  };

  const handleRepositoryChange = (event) => {
    setSelectedRepository((prevValue) => {
      if (prevValue !== event.target.value) {
        setValue('repository', event.target.value);
        setValue('questionnaire', { artifactId: '', shortName: '' });
        setQuestionnaireDefinition();
        for (let index in actionData) {
          setValue(`actionData[${index}].scoreDefinitionName`, '');
        }
      }
      return event.target.value;
    });
  };
  return (
    <Box sx={{ my: 2 }}>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.repositoryTooltip}</Typography>}
            >
              <span>
                {strings.repository}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            name="repository"
            control={control}
            render={({ field: { value, ...field } }) => (
              <RadioGroup
                row
                name="repository"
                value={value}
                onChange={(e) => {
                  handleRepositoryChange(e);
                  field.onChange(e.target.value);
                }}
              >
                <FormControlLabel label={strings.public} value={repositoryTypes.public} control={<Radio />} />
                <FormControlLabel label={strings.private} value={repositoryTypes.private} control={<Radio />} />
              </RadioGroup>
            )}
          />
          <FormHelperText error={Boolean(errors?.repository?.message)}>{errors?.repository?.message}</FormHelperText>
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.dynamicTooltip}</Typography>}
            >
              <span>{strings.dynamic}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="dynamicWidget"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  name="dynamicWidget"
                  checked={field?.value !== undefined ? field?.value : isDynamicWidget}
                  onChange={(event, val) => {
                    setIsDynamicWidget(val);
                    const actions = getValues('actionData');
                    setValue('questionnaire', { shortName: '', artifactId: '' });
                    setValue(
                      `actionData`,
                      actions.filter((action) => action.default),
                    );

                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>

      {isDynamicWidget ? (
        <Box>
          <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.dynamicIdErrorTooltip}</Typography>}
            >
              <span>{strings.dynamicIdError}</span>
            </CambianTooltip>
          </Typography>
          <Paper
            sx={{
              my: 1,
              border: '1px solid #F0F0F0',
              borderRadius: '5px 5px 0 0',
              px: { xs: 2, sm: 3 },
              py: 2,
            }}
          >
            <HeadingAndDescription
              register={register}
              control={control}
              errors={errors}
              headingName="dynamicIdErrorPage.heading"
              descriptionName="dynamicIdErrorPage.description"
              headingPlaceholder={strings.questionnaireIdIsInvalid}
              descriptionPlaceholder={strings.pleaseGetInTouchWithUs}
              enableButton={false}
            />
          </Paper>
        </Box>
      ) : (
        <Grid container alignItems="center" sx={{ mt: 2 }}>
          <Grid item xs={4} lg={3}>
            <Typography>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.questionnaireTooltip}</Typography>}
              >
                <span>
                  {strings.questionnaire}
                  {' *'}
                </span>
              </CambianTooltip>
            </Typography>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              name="questionnaire"
              control={control}
              render={({ field: { ref, ...field } }) => (
                <Autocomplete
                  id="select-questionnaire"
                  {...field}
                  fullWidth
                  size="small"
                  onChange={(_, value) => {
                    handleGetScoreNameList(value?.artifactId);
                    setValue('questionnaire', value || { shortName: '' });
                    field.onChange(value || { shortName: '' });
                  }}
                  getOptionLabel={(question) => question.shortName || ''}
                  inputValue={questionnaireSearchKey}
                  onInputChange={(_, newInputValue) => {
                    setQuestionnaireSearchKey(newInputValue);
                  }}
                  noOptionsText="Unable to retrieve questionnaire"
                  isOptionEqualToValue={(option, value) => option.artifactId === value.artifactId}
                  options={questionnaireList}
                  renderOption={(props, question) => {
                    if (!selectedRepository) {
                      return (
                        <li {...props} aria-selected="false" key={question.artifactId}>
                          <Typography sx={{ color: '#000' }}>{strings.selectRepositoryFirst}</Typography>
                        </li>
                      );
                    }

                    return (
                      <li {...props} key={question.artifactId}>
                        {question?.shortName}
                      </li>
                    );
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={!!errors?.questionnaire}
                      helperText={errors?.questionnaire?.shortName?.message}
                      placeholder={strings.questionnairePlaceholder}
                    />
                  )}
                />
              )}
            />
          </Grid>
        </Grid>
      )}

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.progressBarTooltip}</Typography>}
            >
              <span>{strings.progressLabel}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="showProgressBar"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  name="showProgressBar"
                  checked={field?.value !== undefined ? field?.value : progressBarEnabled}
                  onChange={(event, val) => {
                    setProgressBarEnabled(val);
                    return field?.onChange(val);
                  }}
                  id="progressBarToggle"
                />
              );
            }}
          />
        </Grid>
      </Grid>
      {progressBarEnabled && (
        <Grid container alignItems="center" sx={{ mt: 2 }}>
          <Grid item xs={4} lg={3}>
            <Typography>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.progressPercentageTooltip}</Typography>}
              >
                <span>{strings.progressPercentage}</span>
              </CambianTooltip>
            </Typography>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              control={control}
              name="showProgressPercentage"
              render={({ field: { ref, ...field } }) => {
                return (
                  <Switch
                    size="small"
                    name="showProgressPercentage"
                    checked={field?.value !== undefined ? field?.value : false}
                    onChange={(event, val) => {
                      return field?.onChange(val);
                    }}
                    id="progressPercentageToggle"
                  />
                );
              }}
            />
          </Grid>
        </Grid>
      )}

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.previousButtonTextTooltip}</Typography>}
            >
              <span>{strings.previousButton}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            {...register('previousButtonText')}
            fullWidth
            type="text"
            name="previousButtonText"
            autoComplete="off"
            size="small"
            error={!!errors?.previousButtonText}
            helperText={errors?.previousButtonText?.message}
            placeholder={strings.previous || 'Previous'}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.nextButtonTooltip}</Typography>}
            >
              <span>{strings.nextButton}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            {...register('nextButtonText')}
            fullWidth
            type="text"
            name="nextButtonText"
            autoComplete="off"
            size="small"
            error={!!errors?.nextButtonText}
            helperText={errors?.nextButtonText?.message}
            placeholder={strings.next || 'Next'}
          />
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.doneButtonTextTooltip}</Typography>}
            >
              <span>{strings.doneButton}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            {...register('doneButtonText')}
            fullWidth
            type="text"
            name="doneButtonText"
            autoComplete="off"
            size="small"
            error={!!errors?.doneButtonText}
            helperText={errors?.doneButtonText?.message}
            placeholder={strings.done || 'Done'}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.spinnerTextTooltip}</Typography>}
            >
              <span>{strings.spinner}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <TextField
            {...register('spinnerText')}
            fullWidth
            type="text"
            name="spinnerText"
            autoComplete="off"
            size="small"
            error={!!errors?.spinnerText}
            helperText={errors?.spinnerText?.message}
          />
        </Grid>
      </Grid>

      {enableLogin && (
        <>
          <Box>
            <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.saveForLaterPageTooltip}</Typography>}
              >
                <span>{strings.saveForLater}</span>
              </CambianTooltip>
            </Typography>
            <Paper
              sx={{
                my: 1,
                border: '1px solid #F0F0F0',
                borderRadius: '5px 5px 0 0',
                px: { xs: 2, sm: 3 },
                py: 2,
              }}
            >
              <HeadingAndDescription
                register={register}
                control={control}
                errors={errors}
                headingName="saveForLaterHeading"
                descriptionName="saveForLaterDescription"
                headingPlaceholder={strings.saveForLater}
                descriptionPlaceholder={strings.saveForLaterDescriptionDefaultText}
                enableButton={false}
              />
            </Paper>
          </Box>

          <Box>
            <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
              <CambianTooltip
                placement="right"
                title={<Typography variant="caption">{strings.discardPageTooltip}</Typography>}
              >
                <span>{strings.delete}</span>
              </CambianTooltip>
            </Typography>
            <Paper
              sx={{
                my: 1,
                border: '1px solid #F0F0F0',
                borderRadius: '5px 5px 0 0',
                px: { xs: 2, sm: 3 },
                py: 2,
              }}
            >
              <HeadingAndDescription
                register={register}
                control={control}
                errors={errors}
                headingName="discardHeading"
                descriptionName="discardDescription"
                headingPlaceholder={strings.delete}
                descriptionPlaceholder={strings.discardDescriptionDefaultText}
                enableButton={false}
              />
            </Paper>
          </Box>
        </>
      )}

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.reportTooltip}</Typography>}
            >
              <span>{strings.report}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isReportPageChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  size="small"
                  name="isReportPageChecked"
                  checked={field?.value !== undefined ? field?.value : isReportPageEnabled}
                  onChange={(event, val) => {
                    setIsReportPageEnabled(val);
                    return field?.onChange(val);
                  }}
                  id="reportToggle"
                />
              );
            }}
          />
        </Grid>
      </Grid>
      {isReportPageEnabled && (
        <Paper
          sx={{
            my: 1,
            border: '1px solid #F0F0F0',
            borderRadius: '5px 5px 0 0',
            px: { xs: 2, sm: 3 },
          }}
        >
          <PrintAndDownloadConfigurations
            printIconState="reportPrintEnabled"
            downloadIconState="reportDownloadEnabled"
            saveIconState="reportSaveEnabled"
            enableLogin={enableLogin}
            validationData={validationData}
          />
        </Paper>
      )}
    </Box>
  );
};
