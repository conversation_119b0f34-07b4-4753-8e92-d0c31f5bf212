import React, { useMemo, useState, useEffect } from 'react';
import { Paper, Grid, Typography, Switch, TextField } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';

export const Workflow = (props) => {
  const { validationData, existingWidgetData, widgetsList } = props;
  const { errors, control, setValue, getValues } = validationData;

  const { introduction } = existingWidgetData || {};
  const formIntroValue = getValues('isIntroChecked');
  const [isIntroduction, setIsIntroduction] = useState(formIntroValue ?? introduction?.enabled ?? false);

  return (
    <>
      <Grid container alignItems="center" mb={2}>
        <Grid item xs={4} lg={3}>
          <Typography mt={2}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.introductionTooltip}</Typography>}
            >
              <span>
                {strings.introduction}
                {isIntroduction && ' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isIntroChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  id="introduction-toggle"
                  size="small"
                  checked={isIntroduction}
                  onChange={(event, val) => {
                    setIsIntroduction(val);
                    field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>
      {isIntroduction && (
        <Paper sx={{ my: 1, border: '1px solid #F0F0F0', borderRadius: '5px 5px 0 0', px: { xs: 2, sm: 1 }, py: 2 }}>
          <Grid container rowGap={2} sx={{ my: 2 }}>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <Typography>
                  <span>
                    {strings.heading}
                    {isIntroduction && ' *'}
                  </span>
                </Typography>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  control={control}
                  name="introHeading"
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      size="small"
                      autoComplete="off"
                      type="text"
                      name="introHeading"
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      error={!!errors?.introHeading}
                      helperText={errors?.introHeading?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <Typography>
                  <span>
                    {strings.description}
                    {isIntroduction && ' *'}
                  </span>
                </Typography>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  name="introDescription"
                  control={control}
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      size="small"
                      fullWidth
                      autoComplete="off"
                      multiline
                      minRows={6}
                      name="introDescription"
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      error={!!errors?.introDescription}
                      helperText={errors?.introDescription?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid container alignItems="center">
              <Grid item xs={4} lg={3}>
                <span>{strings.button}</span>
              </Grid>
              <Grid item xs={8} lg={9}>
                <Controller
                  control={control}
                  name={'introButton'}
                  render={({ field: { ref, ...field } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      size="small"
                      placeholder={strings.next}
                      type="text"
                      name={'introButton'}
                      onChange={(event) => {
                        if (event.target.value.trim()) {
                          field.onChange(event.target.value);
                        } else {
                          field.onChange(event.target.value.trim());
                        }
                      }}
                      autoComplete="off"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>
        </Paper>
      )}
    </>
  );
};
