import React from 'react';
import { Checkbox, Autocomplete, TextField, Grid, Button, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import { createFilterOptions } from '@mui/material/Autocomplete';
import strings from '@/utils/localization';

export const MultiSelect = ({
  items,
  selectedValues,
  label,
  placeholder,
  selectAllLabel,
  limitTags,
  onToggleOption,
  onClearOptions,
  onSelectAll,
  getOptionLabel,
  onClickOfSetting,
  validationData,
}) => {
  const { errors, control } = validationData;
  const allSelected = items.length === selectedValues.length;

  const handleToggleSelectAll = (field) => {
    onSelectAll && onSelectAll(!allSelected, field);
  };

  const handleChange = (event, selectedOptions, reason, field) => {
    if (reason === 'selectOption' || reason === 'removeOption') {
      if (selectedOptions.find((option) => option.id === 'select-all')) {
        handleToggleSelectAll(field);
        // field.onChange(items)
      } else {
        onToggleOption && onToggleOption(selectedOptions);
        field.onChange(selectedOptions);
      }
    } else if (reason === 'clear') {
      onClearOptions && onClearOptions();
      field.onChange([]);
    }
  };

  const optionRenderer = (props, location, { selected }) => {
    const selectAllProps =
      location.id === 'select-all' // To control the state of 'select-all' checkbox
        ? { checked: allSelected }
        : {};
    return (
      <Grid container alignItems="center">
        <Grid item xs={12}>
          <li name={location.name} {...props} checked={!selected}>
            <Checkbox name={location.name} checked={selected} {...selectAllProps} />
            {location.name}
          </li>
        </Grid>
        {/* <Grid item xs={4} lg={2}>
          {location.id === 'select-all' ? (
            <></>
          ) : (
            <Button
              htmlFor="setting-id"
              variant="text"
              id="settings-label"
              sx={{
                color: 'text.subHeading',
                textDecoration: 'underline',
                '&:hover': {
                  textDecoration: 'none',
                  bgColor: '#fff',
                  color: '#000',
                },
              }}
              onClick={() => {
                onClickOfSetting(location.cambianReferenceData);
              }}
            >
              view details
            </Button>
          )}
        </Grid> */}
      </Grid>
    );
  };

  const inputRenderer = (params) => (
    <TextField
      {...params}
      placeholder={placeholder}
      name="locations"
      error={!!errors?.locations}
      helperText={`${errors?.locations?.message ? errors?.locations?.message : ''}`}
    />
  );
  const getOptionSelected = (option, anotherOption) => option.id === anotherOption.id;
  const filter = createFilterOptions();
  return (
    <Controller
      name="locations"
      control={control}
      defaultValue={[]}
      render={({ field: { ref, ...field } }) => (
        <Autocomplete
          {...field}
          multiple
          size="small"
          limitTags={limitTags}
          options={items}
          value={selectedValues}
          disableCloseOnSelect
          getOptionLabel={getOptionLabel}
          getOptionSelected={getOptionSelected}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          noOptionsText={<Typography sx={{ color: '#000' }}>{strings.noOptionsAvailable}</Typography>}
          filterOptions={(options, params) => {
            if (options?.length) {
              const filtered = filter(options, params);
              return [
                {
                  id: 'select-all',
                  name: 'Select All',
                },
                ...filtered,
              ];
            } else {
              return [];
            }
          }}
          onChange={(event, selectedOptions, reason) => handleChange(event, selectedOptions, reason, field)}
          renderOption={optionRenderer}
          renderInput={inputRenderer}
        />
      )}
    />
  );
};

MultiSelect.defaultProps = {
  limitTags: 5,
  items: [],
  selectedValues: [],
  getOptionLabel: (value) => value,
};
